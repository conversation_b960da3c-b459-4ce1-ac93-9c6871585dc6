import React, { forwardRef, useImperativeHandle, useState } from 'react';
import { Form, Input, Modal, Select, Tag } from 'antd';
import { UserAddOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';
import { useFormRules } from '@/hooks/common/form';
import { UserInvateSendEmail } from '@/service/api';

interface InviteMemberModalProps {
  mappingData?: {
    shop_country_role: Record<string, string>;
    role_user_status: Record<string, string>;
    user_apply_status: Record<string, string>;
  };
  onSuccess?: () => void;
}

interface InviteMemberFormData {
  email: string;
  country_role: string;
}

export interface InviteMemberModalRef {
  open: (countryId: number) => void;
  close: () => void;
}

const InviteMemberModal = forwardRef<InviteMemberModalRef, InviteMemberModalProps>(
  ({ mappingData = { shop_country_role: {}, role_user_status: {}, user_apply_status: {} }, onSuccess }, ref) => {
    const { t } = useTranslation();
    const [form] = Form.useForm<InviteMemberFormData>();
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);
    const [countryId, setCountryId] = useState<number>(0);
    const { formRules } = useFormRules();
    // 获取角色颜色
    const getRoleColor = (roleKey: string) => {
      const colorMap: Record<string, string> = {
        '0': 'default',
        '1': 'blue',
        '2': 'green',
        '3': 'orange'
      };
      return colorMap[roleKey] || 'default';
    };

    // 获取可选择的角色列表（排除管理员(3)和无权限(0)）
    const getAvailableRoles = () => {
      return Object.entries(mappingData.shop_country_role).filter(([key]) => key !== '3' && key !== '0'); // 排除管理员和无权限
    };

    // 关闭模态框
    const handleClose = () => {
      setVisible(false);
      form.resetFields();
      setCountryId(0);
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        const values = await form.validateFields();
        setLoading(true);

        const requestData = {
          country_id: countryId,
          email: values.email,
          country_role: values.country_role
        };

        const res = await UserInvateSendEmail(requestData);

        if (res && res.data) {
          window.$message?.success(t('page.manage.subaccounts.inviteModal.inviteSuccess'));
          handleClose();
          onSuccess?.();
        }
      } catch (error) {
        console.error('邀请成员失败:', error);
        window.$message?.error(t('page.manage.subaccounts.inviteModal.inviteFailed'));
      } finally {
        setLoading(false);
      }
    };

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: (id: number) => {
        setCountryId(id);
        setVisible(true);
      },
      close: handleClose
    }));

    return (
      <Modal
        title={
          <div className="flex items-center gap-2">
            <UserAddOutlined className="text-blue-500" />
            <span>{t('page.manage.subaccounts.inviteModal.title')}</span>
          </div>
        }
        open={visible}
        onOk={handleSubmit}
        onCancel={handleClose}
        confirmLoading={loading}
        okText={t('page.manage.subaccounts.inviteModal.sendInvite')}
        cancelText={t('common.cancel')}
        centered
        width={520}
        destroyOnClose
      >
        <div className="py-4">
          <div className="mb-4 border border-blue-200 rounded-lg bg-blue-50 p-3">
            <p className="text-sm text-gray-700">{t('page.manage.subaccounts.inviteModal.description')}</p>
          </div>

          <Form
            form={form}
            layout="vertical"
            requiredMark={false}
            autoComplete="off"
          >
            <Form.Item
              name="email"
              label={t('page.manage.subaccounts.inviteModal.emailLabel')}
              rules={formRules.email}
            >
              <Input
                placeholder={t('page.manage.subaccounts.inviteModal.emailPlaceholder')}
                size="large"
                prefix={
                  <Icon
                    icon="material-symbols:email"
                    width={18}
                    height={18}
                    className="text-gray-400"
                  />
                }
              />
            </Form.Item>

            <Form.Item
              name="country_role"
              label={t('page.manage.subaccounts.inviteModal.roleLabel')}
              rules={[
                {
                  required: true,
                  message: t('page.manage.subaccounts.inviteModal.roleRequired')
                }
              ]}
            >
              <Select
                placeholder={t('page.manage.subaccounts.inviteModal.rolePlaceholder')}
                size="large"
                className="w-full"
              >
                {getAvailableRoles().map(([key, value]) => (
                  <Select.Option
                    key={key}
                    value={key}
                  >
                    <div className="flex items-center justify-between">
                      <Tag color={getRoleColor(key)}>{value}</Tag>
                      <span className="ml-2 text-xs text-gray-500">
                        {t(`page.manage.subaccounts.roles.${value.toLowerCase()}`)}
                      </span>
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
        </div>
      </Modal>
    );
  }
);

InviteMemberModal.displayName = 'InviteMemberModal';

export default InviteMemberModal;
