<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>ADS Auth Callback</title>
  </head>
  <body>
    <script>
      (function () {
        try {
          const urlParams = new URLSearchParams(window.location.search);

          const authData = {
            code: urlParams.get('code'),
            state: urlParams.get('state'),
            error: urlParams.get('error'),
            error_description: urlParams.get('error_description')
          };

          if (window.opener) {
            if (authData.error) {
              // 发送错误信息
              window.opener.postMessage(
                {
                  type: 'AMAZON_AUTH_ERROR',
                  error: authData.error,
                  error_description: authData.error_description
                },
                window.location.origin
              );
            } else if (authData.code && authData.state) {
              // 发送成功数据
              window.opener.postMessage(
                {
                  code: authData.code,
                  state: authData.state
                },
                window.location.origin
              );
            }
          }
        } catch (error) {
          if (window.opener) {
            window.opener.postMessage(
              {
                type: 'AMAZON_AUTH_ERROR',
                error: 'CALLBACK_ERROR',
                error_description: error.message
              },
              window.location.origin
            );
          }
        }

        // 立即关闭窗口
        window.close();
      })();
    </script>
  </body>
</html>
