import { QuestionCircleOutlined, SwapOutlined, DeleteOutlined, EditOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';
import { useState, useEffect, useRef } from 'react';

import { GetMyShopRight, ProcessShopRight, WithdrawShopRight } from '@/service/api';
import { useTableScroll } from '@/hooks/common/table';
import InviteMemberModal, { type InviteMemberModalRef } from './modules/InviteMemberModal';

// 定义权限管理相关的类型
interface UserPermission {
  id: number;
  user_vitrual_shop_id: number;
  user_id: number;
  shop_country_relation_addtime: string;
  shop_country_role: string;
  role_user_status: string;
  user_apply_status: string;
  user_apply_datetime: string | null;
  agree_datetime: string;
  shop_country_id: number;
  shop_country_sp_datetime: string | null;
  shop_country_ad_datetime: string | null;
  user_shop_country_sp_flag: string | null;
  user_shop_country_ad_flag: string | null;
  email: string;
}

interface CountryData {
  login_user_id: number;
  login_user_role: string;
  country_id: number;
  country_name: string;
  user_list: UserPermission[];
}

interface ShopRightData {
  shop_right: Record<string, Record<string, Record<string, CountryData>>>;
  shop_country_role: Record<string, string>;
  role_user_status: Record<string, string>;
  user_apply_status: Record<string, string>;
}

interface TableDataItem {
  key: string;
  type: 'shop' | 'region' | 'country' | 'user';
  shop_name?: string;
  region?: string;
  country_code?: string;
  country_name?: string;
  email?: string;
  current_user_role?: string;
  shop_country_id?: number;
  relation_id?: number;
  role?: string;
  role_name?: string;
  role_color?: string;
  member_status?: string;
  member_status_name?: string;
  member_status_color?: string;
  approval_status?: string;
  approval_status_name?: string;
  approval_status_color?: string;
  user_data?: UserPermission;
  children?: TableDataItem[];
  isParent?: boolean;
  isSon?: boolean;
}

// 获取大洲信息的本地函数
const getContinentsName = (countryCode: string) => {
  const shopNameMap: Record<string, { name: string; icon: string }> = {
    NA: {
      name: '北美',
      icon: 'emojione:globe-showing-americas'
    },
    EU: {
      name: '欧洲',
      icon: 'emojione:globe-showing-europe-africa'
    },
    JP: {
      name: '日本',
      icon: 'emojione:globe-showing-asia-australia'
    },
    SG: {
      name: '新加坡',
      icon: 'emojione:globe-showing-asia-australia'
    },
    AU: {
      name: '澳大利亚',
      icon: 'emojione:globe-showing-asia-australia'
    },
    SA: {
      name: '沙特阿拉伯',
      icon: 'emojione:globe-showing-europe-africa'
    },
    AE: {
      name: '阿联酋',
      icon: 'emojione:globe-showing-europe-africa'
    }
  };
  return shopNameMap[countryCode] || { name: countryCode, icon: 'emojione:globe-showing-americas' };
};

export function Component() {
  const { t } = useTranslation();
  const { tableWrapperRef, scrollConfig } = useTableScroll();

  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<TableDataItem[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [changeRoleModalVisible, setChangeRoleModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<TableDataItem | null>(null);
  const [selectedNewRole, setSelectedNewRole] = useState<string>('');
  
  // 移交权限模态框状态
  const [transferModalVisible, setTransferModalVisible] = useState(false);
  const [selectedTransferUser, setSelectedTransferUser] = useState<string>('');
  const [transferRecord, setTransferRecord] = useState<TableDataItem | null>(null);
  
  // 邀请成员模态框的ref
  const inviteMemberModalRef = useRef<InviteMemberModalRef>(null);

  // 获取角色颜色
  const getRoleColor = (roleKey: string) => {
    const colorMap: Record<string, string> = {
      '0': 'default',
      '1': 'blue',
      '2': 'orange',
      '3': 'green'
    };
    return colorMap[roleKey] || 'default';
  };

  // 获取成员状态颜色
  const getMemberStatusColor = (statusKey: string) => {
    const colorMap: Record<string, string> = {
      '0': 'default',
      '1': 'success',
      '2': 'processing',
      '3': 'error'
    };
    return colorMap[statusKey] || 'default';
  };

  // 获取审批状态颜色
  const getApprovalStatusColor = (statusKey: string) => {
    const colorMap: Record<string, string> = {
      '0': 'default',
      '1': 'processing',
      '2': 'error',
      '3': 'success',
      '4': 'warning',
      '5': 'default'
    };
    return colorMap[statusKey] || 'default';
  };
  const [mappingData, setMappingData] = useState<{
    shop_country_role: Record<string, string>;
    role_user_status: Record<string, string>;
    user_apply_status: Record<string, string>;
  }>({
    shop_country_role: {},
    role_user_status: {},
    user_apply_status: {}
  });

  // 数据转换函数：将嵌套结构转换为四级表格数据
  const transformDataToTable = (data: ShopRightData): TableDataItem[] => {
    const result: TableDataItem[] = [];
    const allExpandKeys: React.Key[] = [];
    
    if (!data.shop_right) return result;

    Object.entries(data.shop_right).forEach(([shopName, regions]) => {
      const regionChildren: TableDataItem[] = [];
      
      // 添加 shop 级别的 key 到展开列表
      allExpandKeys.push(shopName);

      Object.entries(regions).forEach(([regionCode, countries]) => {
        const countryChildren: TableDataItem[] = [];
        const regionKey = `${shopName}-${regionCode}`;
        
        // 添加 region 级别的 key 到展开列表
        allExpandKeys.push(regionKey);
        console.log(countries,"countries====")

        Object.entries(countries).forEach(([countryCode, countryData]) => {
          const userChildren: TableDataItem[] = countryData.user_list.map((user, index) => ({
            key: `${shopName}-${regionCode}-${countryCode}-user-${user.id}-${index}`,
            type: 'user',
            email: user.email,
            // 当前用户角色
            current_user_role:countryData.login_user_role,
            shop_country_id:user.shop_country_id,
            relation_id:user.id,
            role: user.shop_country_role,
            role_name: data.shop_country_role[user.shop_country_role] || user.shop_country_role,
            role_color: getRoleColor(user.shop_country_role),
            member_status: user.role_user_status,
            member_status_name: data.role_user_status[user.role_user_status] || user.role_user_status,
            member_status_color: getMemberStatusColor(user.role_user_status),
            approval_status: user.user_apply_status,
            approval_status_name: data.user_apply_status[user.user_apply_status] || user.user_apply_status,
            approval_status_color: getApprovalStatusColor(user.user_apply_status),
            user_data: user,
            isSon: true
          }));

          const countryKey = `${shopName}-${regionCode}-${countryCode}`;
          // 如果有用户数据，添加 country 级别的 key 到展开列表
          if (userChildren.length > 0) {
            allExpandKeys.push(countryKey);
          }

          countryChildren.push({
            key: countryKey,
            type: 'country',
            current_user_role: countryData.login_user_role,
            shop_country_id: countryData.country_id,
            country_code: countryCode,
            country_name: countryData.country_name,
            children: userChildren.length > 0 ? userChildren : []  // 修改：使用空数组而不是undefined
          });
        });

        regionChildren.push({
          key: regionKey,
          type: 'region',
          region: regionCode,
          children: countryChildren
        });
      });

      result.push({
        key: shopName,
        type: 'shop',
        shop_name: shopName,
        children: regionChildren,
        isParent: true
      });
    });

    // 设置展开的keys
    setExpandedRowKeys(allExpandKeys);
    return result;
  };

  // 获取权限数据
  const fetchPermissionData = async () => {
    try {
      setLoading(true);
      const response = await GetMyShopRight({});
      
      if (response && response.data) {
        // 保存映射数据
        setMappingData({
          shop_country_role: response.data.shop_country_role || {},
          role_user_status: response.data.role_user_status || {},
          user_apply_status: response.data.user_apply_status || {}
        });
        
        const transformedData = transformDataToTable(response.data);
        console.log(transformedData, "transformedData===");
        setTableData(transformedData);
      }
    } catch (error) {
      console.error('获取权限数据失败:', error);
      // window.$message?.error(t('page.manage.subaccounts.messages.fetchDataFailed'));
    } finally {
      setLoading(false);
    }
  };

  // 处理权限操作
  const handleProcessShopRight = async (
    record: TableDataItem,
    opType: string,
    confirmMessage: string,
    newRole?: string,
    modalType: 'confirm' | 'warning' | 'error' | 'info' = 'confirm',
    transferToEmail?: string
  ) => {
    try {
      // 根据操作类型选择不同的modal样式
      const modalConfig: any = {
        title: t('common.confirm'),
        content: confirmMessage,
        okText: t('common.confirm'),
        cancelText: t('common.cancel'),
        centered: true,
        width: 400
      };

             // 根据操作类型设置不同的样式和图标
       switch (opType) {
         case 'switch_admin':
           modalConfig.type = 'warning';
           modalConfig.icon = null; // 隐藏左侧图标
           modalConfig.okButtonProps = { danger: true };
           modalConfig.title = (
             <div className="flex items-center gap-2">
               <SwapOutlined className="text-orange-500" />
               <span>{t('page.manage.subaccounts.confirmTitles.transferPermission')}</span>
             </div>
           );
           break;
         case 'remove':
           modalConfig.type = 'error';
           modalConfig.icon = null; // 隐藏左侧图标
           modalConfig.okButtonProps = { danger: true };
           modalConfig.title = (
             <div className="flex items-center gap-2">
               <DeleteOutlined className="text-red-500" />
               <span>{t('page.manage.subaccounts.confirmTitles.removePermission')}</span>
             </div>
           );
           break;
         case 'change_role':
           modalConfig.type = 'info';
           modalConfig.icon = null; // 隐藏左侧图标
           modalConfig.title = (
             <div className="flex items-center gap-2">
               <EditOutlined className="text-blue-500" />
               <span>{t('page.manage.subaccounts.confirmTitles.changeRole')}</span>
             </div>
           );
           break;
         case 'agree':
           modalConfig.type = 'success';
           modalConfig.icon = null; // 隐藏左侧图标
           modalConfig.okButtonProps = { type: 'primary' };
           modalConfig.title = (
             <div className="flex items-center gap-2">
               <CheckCircleOutlined className="text-green-500" />
               <span>{t('page.manage.subaccounts.confirmTitles.agreeApplication')}</span>
             </div>
           );
           break;
         case 'reject':
           modalConfig.type = 'warning';
           modalConfig.icon = null; // 隐藏左侧图标
           modalConfig.okButtonProps = { danger: true };
           modalConfig.title = (
             <div className="flex items-center gap-2">
               <CloseCircleOutlined className="text-red-500" />
               <span>{t('page.manage.subaccounts.confirmTitles.rejectApplication')}</span>
             </div>
           );
           break;
         default:
           modalConfig.type = 'confirm';
           modalConfig.icon = null; // 隐藏左侧图标
       }

      const confirmed = await window.$modal?.confirm(modalConfig);

      if (!confirmed) return;

      const requestData: any = {
        shop_country_id: record.shop_country_id,
        relation_id: record.user_data?.id,
        op_type: opType
      };

      if (newRole) {
        requestData.shop_country_role = newRole;
      }

      if (transferToEmail) {
        requestData.transfer_to_email = transferToEmail;
      }

      const res = await ProcessShopRight(requestData);
      console.log(res,"res===")
      if(res && res.data){
        window.$message?.success(t('page.manage.subaccounts.messages.operationSuccess'));
        setChangeRoleModalVisible(false);
        setSelectedRecord(null);
        setSelectedNewRole('');
        // 重置移交权限模态框状态
        setTransferModalVisible(false);
        setTransferRecord(null);
        setSelectedTransferUser('');
      }
   
      await fetchPermissionData();
    } catch (error) {
      console.error('操作失败:', error);
      // window.$message?.error(t('page.manage.subaccounts.messages.operationFailed'));
    }
  };

  // 撤销申请
  const handleWithdrawShopRight = async (record: TableDataItem) => {
    try {
      const confirmed = await window.$modal?.confirm({
        title: (
          <div className="flex items-center gap-2">
            <CloseCircleOutlined className="text-orange-500" />
            <span>{t('page.manage.subaccounts.confirmTitles.withdrawApplication')}</span>
          </div>
        ),
        content: t('page.manage.subaccounts.confirmMessages.withdrawApplication', { email: record.email }),
        okText: t('common.confirm'),
        cancelText: t('common.cancel'),
        type: 'warning',
        icon: null, // 隐藏左侧图标
        okButtonProps: { danger: true },
        centered: true,
        width: 400
      });

      if (!confirmed) return;

      const res = await WithdrawShopRight({
        relation_id: record.user_data?.id
      });
      console.log(res,"res===")
      if(res && res.data){
        window.$message?.success(t('page.manage.subaccounts.messages.operationSuccess'));
        await fetchPermissionData();
      }
    } catch (error) {
      console.error('撤销申请失败:', error);
      // window.$message?.error(t('page.manage.subaccounts.messages.operationFailed'));
    }
  };

  // 移交权限 - 打开选择用户模态框
  const handleTransferPermission = (record: TableDataItem) => {
    // 检查当前站点下是否有其他用户
    const currentSiteUsers = getCurrentSiteUsers(record.shop_country_id);
    const availableUsers = currentSiteUsers.filter((user: { email: string; role_name: string; role_color: string; shop_country_id?: number }) => user.email !== record.email);
    
    if (availableUsers.length === 0) {
      window.$modal?.info({
        title: t('page.manage.subaccounts.transfer.noUsersTitle'),
        content: t('page.manage.subaccounts.transfer.noUsersContent'),
        okText: t('common.confirm'),
        centered: true,
        width: 400,
        icon: null
      });
      return;
    }
    
    setTransferRecord(record);
    setSelectedTransferUser('');
    setTransferModalVisible(true);
  };

  // 确认移交权限
  const handleConfirmTransfer = () => {
    if (!transferRecord || !selectedTransferUser) {
      window.$message?.warning(t('page.manage.subaccounts.transfer.pleaseSelectUser'));
      return;
    }

    handleProcessShopRight(
      transferRecord,
      'switch_admin',
      t('page.manage.subaccounts.transfer.confirmMessage', { 
        fromEmail: transferRecord.email,
        toEmail: selectedTransferUser 
      }),
      undefined,
      'warning',
      selectedTransferUser
    );
  };

  // 移除权限
  const handleRemovePermission = (record: TableDataItem) => {
    handleProcessShopRight(
      record,
      'remove',
      t('page.manage.subaccounts.confirmMessages.removePermission', { email: record.email }),
      undefined,
      'error'
    );
  };

  // 同意申请
  const handleAgreeApplication = (record: TableDataItem) => {
    handleProcessShopRight(
      record,
      'agree',
      t('page.manage.subaccounts.confirmMessages.agreeApplication', { email: record.email }),
      undefined,
      'confirm'
    );
  };

  // 拒绝申请
  const handleRejectApplication = (record: TableDataItem) => {
    handleProcessShopRight(
      record,
      'reject',
      t('page.manage.subaccounts.confirmMessages.rejectApplication', { email: record.email }),
      undefined,
      'warning'
    );
  };

  // 打开变更权限模态框
  const handleOpenChangeRoleModal = (record: TableDataItem) => {
    setSelectedRecord(record);
    setSelectedNewRole('');
    setChangeRoleModalVisible(true);
  };

  // 确认变更权限
  const handleConfirmChangeRole = () => {
    if (!selectedRecord || !selectedNewRole) {
      window.$message?.warning(t('page.manage.subaccounts.messages.pleaseSelectRole'));
      return;
    }

    handleProcessShopRight(
      selectedRecord,
      'change_role',
      t('page.manage.subaccounts.confirmMessages.changeRole', { 
        email: selectedRecord.email,
        newRole: mappingData.shop_country_role[selectedNewRole]
      }),
      selectedNewRole,
      'info'
    );
  };

  // 获取可选择的角色列表（排除管理员和当前用户角色）
  const getAvailableRoles = (currentRole?: string) => {
    return Object.entries(mappingData.shop_country_role)
      .filter(([key]) => key !== '3' && key !== currentRole); // 排除管理员(3)和当前用户角色
  };

  // 获取指定站点下的所有用户
  const getCurrentSiteUsers = (siteId?: number) => {
    const users: { email: string; role_name: string; role_color: string; shop_country_id?: number }[] = [];
    
    const findUsers = (items: TableDataItem[]) => {
      items.forEach(item => {
        if (item.type === 'user' && item.email && item.shop_country_id === siteId) {
          users.push({
            email: item.email,
            role_name: item.role_name || '',
            role_color: item.role_color || 'default',
            shop_country_id: item.shop_country_id
          });
        }
        if (item.children) {
          findUsers(item.children);
        }
      });
    };
    
    findUsers(tableData);
    return users;
  };

  // 获取当前站点下的用户列表（用于移交权限）
  const getTransferableUsers = () => {
    if (!transferRecord?.shop_country_id) return [];
    
    const currentSiteUsers = getCurrentSiteUsers(transferRecord.shop_country_id);
    // 排除当前要移交的用户，只显示其他用户
    return currentSiteUsers.filter(user => user.email !== transferRecord?.email);
  };

  // 渲染操作按钮
  const renderActionButtons = (record: TableDataItem) => {
    const buttons = [];

    // 1. 移交权限按钮
    if (record.current_user_role === '3' && record.role === '3') {
      buttons.push(
        <AButton
          key="transfer"
          type="link"
          size="small"
          // warning 按钮
          className="text-orange-500 hover:text-orange-600"
          icon={<SwapOutlined />}
          onClick={() => handleTransferPermission(record)}
        >
          {t('page.manage.subaccounts.actions.transferPermission')}
        </AButton>
      );
    }

    // 2. 移除权限和变更权限按钮
    if (record.current_user_role === '3' && record.role !== '3' && record.approval_status === '3') {
      buttons.push(
        <AButton
          key="remove"
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemovePermission(record)}
        >
          {t('page.manage.subaccounts.actions.removePermission')}
        </AButton>
      );

      // 排除管理员角色和当前用户角色的选项
      const availableRoles = getAvailableRoles(record.role);
      if (availableRoles.length > 0) {
        buttons.push(
          <AButton
            key="change"
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleOpenChangeRoleModal(record)}
          >
            {t('page.manage.subaccounts.actions.changePermission')}
          </AButton>
        );
      }
    }

    // 3. 同意和拒绝按钮
    if (record.current_user_role === '3' && (record.approval_status === '6' || record.approval_status === '7')) {
      buttons.push(
        <AButton
          key="agree"
          type="link"
          size="small"
          icon={<CheckCircleOutlined />}
          onClick={() => handleAgreeApplication(record)}
        >
          {t('page.manage.subaccounts.actions.agree')}
        </AButton>
      );

      buttons.push(
        <AButton
          key="reject"
          type="link"
          size="small"
          danger
          onClick={() => handleRejectApplication(record)}
          icon={<CloseCircleOutlined />}
        >
          {t('page.manage.subaccounts.actions.reject')}
        </AButton>
      );
    }

    // 4. 撤销申请按钮
    if (record.current_user_role === record.role && (record.approval_status === '6' || record.approval_status === '7')) {
      buttons.push(
        <AButton
          key="withdraw"
          type="link"
          size="small"
          onClick={() => handleWithdrawShopRight(record)}
        >
          {t('page.manage.subaccounts.actions.withdrawApplication')}
        </AButton>
      );
    }

    return buttons.length > 0 ? (
      <div className="flex flex-wrap gap-1 justify-center">
        {buttons}
      </div>
    ) : null;
  };

  // 表格列定义
  const columns = [
    {
      key: 'shop',
      title: '',
      width: 100,
      dataIndex: 'shop_name',
      render: (text: any, record: any) => {
        if (record.isParent) {
          return (
            <div
              className="absolute left-10 z-9 flex items-center gap-2 whitespace-nowrap"
              style={{ top: '52%', transform: 'translateY(-50%)' }}
            >
              <p className="flex items-center text-sm text-gray-500 font-500">
                {t('page.manage.subaccounts.columns.shopName')}：<span className="text-black">{text}</span>
              </p>
            </div>
          );
        } else if (record.type === 'region') {
          return (
            <div className="absolute left-14 z-9 flex items-center gap-2 whitespace-nowrap">
              <Icon
                icon={getContinentsName(record.region)?.icon}
                width={24}
                height={24}
              />
              <p className="text-sm font-500">{t(`page.setting.country.${record.region}`)}</p>
            </div>
          );
        }
      }
    },
    {
      key: 'country',
      title: t('page.manage.subaccounts.columns.country'),
      dataIndex: 'country_code',
      width: 150,
      render: (text: any, record: any, index: number) => {
        return (
          <>
            {record.type === 'country' && (
              <div className="flex items-center absolute top-[13px] whitespace-nowrap">
                <Icon
                  className="mr-1"
                  icon={`circle-flags:${text?.toLowerCase()}`}
                  width={25}
                  height={25}
                />
                <span className="whitespace-nowrap"> {t(`page.setting.country.${text}`)} ({record.country_name})</span>
              </div>
            )}
          </>
        );
      }
    },
    {
      title: t('page.manage.subaccounts.columns.email'),
      dataIndex: 'email',
      key: 'email',
      width: 200,
      render: (text: string, record: TableDataItem) => {
        if (record.type === 'user') {
          return (
            <div className="flex items-center gap-2">
              {text}
            </div>
          );
        }
        return null;
      }
    },
    {
      title: t('page.manage.subaccounts.columns.role'),
      dataIndex: 'role',
      key: 'role',
      width: 120,
      align: 'center' as const,
      render: (_: string, record: TableDataItem) => {
        if (record.type === 'user') {
          return <ATag color={record.role_color}>{record.role_name}</ATag>;
        }
        return null;
      }
    },
    {
      title: t('page.manage.subaccounts.columns.memberStatus'),
      dataIndex: 'member_status',
      key: 'member_status',
      width: 120,
      align: 'center' as const,
      render: (_: string, record: TableDataItem) => {
        if (record.type === 'user') {
          return <ATag color={record.member_status_color}>{record.member_status_name}</ATag>;
        }
        return null;
      }
    },
    {
      title: t('page.manage.subaccounts.columns.approvalStatus'),
      dataIndex: 'approval_status',
      key: 'approval_status',
      width: 120,
      align: 'center' as const,
      render: (_: string, record: TableDataItem) => {
        if (record.type === 'user') {
          return <ATag color={record.approval_status_color}>{record.approval_status_name}</ATag>;
        }
        return null;
      }
    },
    {
       key: 'operation',
      title: t('page.setting.auth.operation'),
      dataIndex: 'operation',
      width: 200,
      align: 'center' as const,
      render: (text: string, record: TableDataItem) => {
        if (record.type === 'user') {
          return renderActionButtons(record);
        } else if (record.type === 'country') {
          // 如果当前角色为2或3，展示邀请成员按钮
          if (record.current_user_role === '2' || record.current_user_role === '3') {
            return (
              <AButton 
                type="link" 
                size="small" 
                icon={<Icon icon="material-symbols:person-add" width={16} height={16} />}
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡，防止表格折叠
                  console.log(record, "record===");
                  handleInviteMember(record);
                }}
              >
                {t('page.manage.subaccounts.actions.inviteMember')}
              </AButton>
            );
          }
        }
        return null;
      }
    }
  ];

  useEffect(() => {
    fetchPermissionData();
  }, []);

  const PermissionDescription = () => {
    const content = (
      <div className="p-4 max-w-md">
        <div className="mb-4 border-b border-blue-100 pb-3 text-lg text-primary font-bold">
          {t('page.manage.subaccounts.description')}
        </div>

        <div className="mb-4 rounded-lg bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border border-blue-100">
          <div className="mb-3 flex items-center text-base text-gray-800 font-semibold">
            <span className="mr-2 h-4 w-1 rounded-full bg-gradient-to-b from-blue-500 to-blue-600"></span>
            {t('page.manage.subaccounts.roleDescription')}
          </div>
          <div className="space-y-3">
            {Object.entries(mappingData.shop_country_role).map(([key, value]) => (
              <div key={key} className="flex items-center gap-2">
                <ATag color={getRoleColor(key)}>{value}</ATag>
                <span className="text-sm text-gray-600">{t(`page.manage.subaccounts.roles.${value.toLowerCase()}`)}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="rounded-lg bg-gradient-to-r from-green-50 to-emerald-50 p-4 border border-green-100">
          <div className="mb-3 flex items-center text-base text-gray-800 font-semibold">
            <span className="mr-2 h-4 w-1 rounded-full bg-gradient-to-b from-green-500 to-green-600"></span>
            {t('page.manage.subaccounts.statusDescription')}
          </div>
          <div className="text-sm text-gray-600 space-y-1">
            <p className="flex items-start gap-2">
              <span className="text-green-500 mt-1">•</span>
              {t('page.manage.subaccounts.statusDescriptionText.memberStatus')}
            </p>
            <p className="flex items-start gap-2">
              <span className="text-green-500 mt-1">•</span>
              {t('page.manage.subaccounts.statusDescriptionText.approvalStatus')}
            </p>
          </div>
        </div>
      </div>
    );
    return content;
  };

  // 邀请成员处理函数
  const handleInviteMember = (record: TableDataItem) => {
    console.log('邀请成员:', record);
    if (record.shop_country_id && inviteMemberModalRef.current) {
      inviteMemberModalRef.current.open(record.shop_country_id);
    }
  };

  return (
    <div className="h-full min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
      <ACard
        ref={tableWrapperRef}
        bordered={false}
        extra={null}
        title={
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <span className="text-lg font-semibold text-gray-800">{t('page.manage.subaccounts.title')}</span>
            </div>
            <APopover
              content={PermissionDescription}
              title={null}
              placement="bottom"
              overlayClassName="min-w-sm"
            >
              <QuestionCircleOutlined className="cursor-pointer text-sm text-primary transition-colors duration-200" />
            </APopover>
          </div>
        }
        className="flex-col-stretch sm:flex-1-hidden card-wrapper shadow-sm"
      >
        <ATable
          loading={loading}
          dataSource={tableData}
          columns={columns}
          scroll={scrollConfig}
          size="middle"
          expandable={{
            expandRowByClick: true,
            expandedRowKeys: expandedRowKeys,  // 添加：使用受控的展开状态
            onExpandedRowsChange: (keys) => setExpandedRowKeys([...keys]),  // 添加：监听展开状态变化
            indentSize: 20
          }}
          pagination={{
            showQuickJumper: true,
            showSizeChanger: false,
            showTotal: (total, range) => t('page.camtarListing.total', { total }),
          }}
          className="modern-table"
          rowClassName={(record) => {
            if (record.type === 'shop') return 'shop-row bg-#F4F5F8 hover:bg-blue-50/50';
            if (record.type === 'region') return 'region-row bg-green-50/20 hover:bg-green-50/40';
            if (record.type === 'country') return 'country-row bg-yellow-50/20 hover:bg-yellow-50/40';
            return 'user-row hover:bg-gray-50/50';
          }}
        />
      </ACard>

      {/* 邀请成员模态框 */}
      <InviteMemberModal
        ref={inviteMemberModalRef}
        mappingData={mappingData}
        onSuccess={fetchPermissionData}
      />

      {/* 移交权限模态框 */}
      <AModal
        title={
          <div className="flex items-center gap-2">
            <SwapOutlined className="text-orange-500" />
            <span>{t('page.manage.subaccounts.transfer.modalTitle')}</span>
          </div>
        }
        open={transferModalVisible}
        onOk={handleConfirmTransfer}
        onCancel={() => {
          setTransferModalVisible(false);
          setTransferRecord(null);
          setSelectedTransferUser('');
        }}
        okText={t('page.manage.subaccounts.transfer.confirmTransfer')}
        cancelText={t('common.cancel')}
        centered
        width={520}
        okButtonProps={{
          disabled: !selectedTransferUser,
          danger: true
        }}
      >
        <div className="py-4">
          <div className="mb-4 p-3 bg-orange-50 rounded-lg border border-orange-200">
            <p className="text-sm text-gray-700 mb-2">
              <span className="font-medium">{t('page.manage.subaccounts.transfer.currentAdmin')}：</span>
              <span className="text-orange-600">{transferRecord?.email}</span>
            </p>
            <p className="text-sm text-red-600">
              <span className="font-medium">{t('page.manage.subaccounts.transfer.warning')}：</span>
              {t('page.manage.subaccounts.transfer.warningText')}
            </p>
          </div>
          
          <div>
            <p className="mb-3 text-sm font-medium text-gray-700">
              {t('page.manage.subaccounts.transfer.selectUser')}：
            </p>
            <ASelect
              placeholder={t('page.manage.subaccounts.transfer.selectUserPlaceholder')}
              value={selectedTransferUser}
              onChange={setSelectedTransferUser}
              className="w-full"
              size="large"
              showSearch
              filterOption={(input, option) =>
                (option?.children as any)?.toString().toLowerCase().includes(input.toLowerCase())
              }
            >
              {getTransferableUsers().map((user) => (
                <ASelect.Option key={user.email} value={user.email}>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{user.email}</span>
                    <ATag color={user.role_color} className="ml-2">
                      {user.role_name}
                    </ATag>
                  </div>
                </ASelect.Option>
              ))}
            </ASelect>
          </div>
        </div>
      </AModal>

      {/* 变更权限模态框 */}
      <AModal
        title={
          <div className="flex items-center gap-2">
            <EditOutlined className="text-blue-500" />
            <span>{t('page.manage.subaccounts.changeRoleModal.title')}</span>
          </div>
        }
        open={changeRoleModalVisible}
        onOk={handleConfirmChangeRole}
        onCancel={() => {
          setChangeRoleModalVisible(false);
          setSelectedRecord(null);
          setSelectedNewRole('');
        }}
        okText={t('common.confirm')}
        cancelText={t('common.cancel')}
        centered
        width={480}
        okButtonProps={{
          disabled: !selectedNewRole
        }}
      >
        <div className="py-4">
          <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-gray-700 mb-2">
              <span className="font-medium">{t('page.manage.subaccounts.changeRoleModal.currentUser')}：</span>
              <span className="text-blue-600">{selectedRecord?.email}</span>
            </p>
            <p className="text-sm text-gray-700">
              <span className="font-medium">{t('page.manage.subaccounts.changeRoleModal.currentRole')}：</span>
              <ATag color={selectedRecord?.role_color}>{selectedRecord?.role_name}</ATag>
            </p>
          </div>
          
          <div>
            <p className="mb-3 text-sm font-medium text-gray-700">
              {t('page.manage.subaccounts.changeRoleModal.selectNewRole')}：
            </p>
            <ASelect
              placeholder={t('page.manage.subaccounts.changeRoleModal.selectRole')}
              value={selectedNewRole}
              onChange={setSelectedNewRole}
              className="w-full"
              size="large"
            >
              {getAvailableRoles(selectedRecord?.role).map(([key, value]) => (
                <ASelect.Option key={key} value={key}>
                  <div className="flex items-center justify-between">
                    <ATag color={getRoleColor(key)}>{value}</ATag>
                    <span className="text-xs text-gray-500 ml-2">
                      {t(`page.manage.subaccounts.roles.${value.toLowerCase()}`)}
                    </span>
                  </div>
                </ASelect.Option>
              ))}
            </ASelect>
          </div>
        </div>
      </AModal>
    </div>
  );
}
