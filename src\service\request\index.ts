import type { AxiosResponse } from 'axios';
import { BACKEND_ERROR_CODE, createFlatRequest, createRequest } from '@sa/axios';
import { resetStore } from '@/store/slice/auth';
import { store } from '@/store';
import { $t } from '@/locales';
import { localStg } from '@/utils/storage';
import { getServiceBaseURL } from '@/utils/service';
import { getLocalDataToken } from '@/service/api/local';
import { getAuthorization, handleExpiredRequest, showErrorMsg } from './shared';
import type { RequestInstanceState } from './type';
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

interface LocalTokenData {
  Authorization: string;
  Expires_in: number;
  Timestamp: string;
  UserId: string;
  expiryTime: number;
}

// 定义白名单URL列表
// const whitelistUrls = [
//   '/listing_sum_data',
//   '/listing_parent_data',
//   '/get_authed_listings_sum',
//   '/get_asin_campaign_sum',
//   '/get_campaign_keyword_data'
// ];

const localServiceBaseURL = import.meta.env.VITE_LOCAL_SERVICE_BASE_URL;

// 不需要UID的URL列表
const noUidUrls = import.meta.env.VITE_NO_UID_URLS?.split(',') || [];

// 检查请求URL是否在白名单中
const whitelistUrls = import.meta.env.VITE_WHITELIST_URLS?.split(',') || [];

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const request = createFlatRequest<App.Service.Response, RequestInstanceState>(
  {
    baseURL
  },
  {
    async onRequest(config) {
      const userInfo = localStg.get('userInfo') || {};
      const token = localStg.get('token');
      // const Authorization = token ? `Bearer ${token}` : null;
      const Authorization = token || null;
      Object.assign(config.headers, { Authorization });

      // const Authorization = getAuthorization();
      // Object.assign(config.headers, { Authorization });

      const isWhitelisted = whitelistUrls.some((url: string) => config.url?.includes(url));
      if (isWhitelisted) {
        config.baseURL = localServiceBaseURL;
        // 检查本地存储中的token是否存在且未过期
        let localTokenData: LocalTokenData | null = localStg.get('localToken') as LocalTokenData | null;
        const currentTime = Math.floor(Date.now() / 1000); // 当前时间的时间戳（秒）
        // console.log(localTokenData, 'localTokenData11111');
        if (!localTokenData || (localTokenData && currentTime >= localTokenData.expiryTime)) {
          // console.log('重新获取2222--', currentTime);
          // 如果token不存在或已过期，重新获取
          const tokenResponse = await getLocalDataToken();
          const { Authorization, Expires_in, Timestamp, UserId } = tokenResponse.data;

          // 计算过期时间
          const expiryTime = Number.parseInt(Timestamp) + Number.parseInt(Expires_in);

          // 存储token数据到本地
          localTokenData = { Authorization, Expires_in, Timestamp, UserId, expiryTime };
          localStg.set('localToken', localTokenData);
        }

        if (localTokenData && localTokenData.Authorization) {
          Object.assign(config.headers, {
            Authorization: localTokenData.Authorization,
            Timestamp: localTokenData.Timestamp,
            UserId: localTokenData.UserId
          });
        } else {
          throw new Error('无法获取本地服务token');
        }
        // console.log(import.meta.env, 'import.meta.env');
        // 判断是否是本地测试
        const isLocalTest = import.meta.env.MODE === 'test';
        if (isLocalTest) {
          // 本地测试 ClearCache
          config.data = {
            ...config.data
            // ClearCache: 1
          };
        }
      }
      config.retryCount = config.retryCount || 0;
      // 只有不在 noUidUrls 列表中，才添加 UID
      if (noUidUrls.includes(config.url)) {
        if (config.method?.toLowerCase() === 'get') {
          // 处理 GET 请求，将 UID 添加到查询参数中
          const separator = config.url.includes('?') ? '&' : '?';
          config.url += `${separator}UID=${encodeURIComponent(String(userInfo.active_shop_id))}`;
        } else {
          // 检查 UID 是否为 0
          // if (Number(userInfo?.active_shop_id) === 0) {
          //   console.warn('UID is 0, request will not be sent.');
          //   return Promise.reject(new Error('UID is 0, request aborted.'));
          // }
          // 处理 POST 请求，将 UID 添加到请求体中
          config.data = {
            UID: Number(userInfo?.active_shop_id),
            ...config.data
          };
        }
      }
      return config;
    },
    isBackendSuccess(response) {
      // when the backend response code is "0000"(default), it means the request is success
      // to change this logic by yourself, you can modify the `VITE_SERVICE_SUCCESS_CODE` in `.env` file
      // console.log(response,String(response.data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE,'response');
      return String(response.data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE;
    },
    async onBackendFail(response, instance) {
      console.log(response, 'response');
      // console.log(response.data, 'response.data');
      const responseCode = String(response.data.code);

      function handleLogout() {
        store.dispatch(resetStore());
      }

      function logoutAndCleanup() {
        handleLogout();
        window.removeEventListener('beforeunload', handleLogout);

        request.state.errMsgStack = request.state.errMsgStack.filter(msg => msg !== response.data.msg);
        // 重定向到登录页面
        window.location.href = '/login';
      }

      // when the backend response code is in `logoutCodes`, it means the user will be logged out and redirected to login page
      const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
      console.log(logoutCodes, responseCode, 'logoutCodes');
      if (logoutCodes.includes(responseCode)) {
        handleLogout();
        // 重定向到登录页面
        window.location.href = '/login';
        return null;
      }

      // 获取白名单 URL 列表
      // const whitelistUrls = import.meta.env.VITE_WHITELIST_URLS?.split(',') || [];
      const isWhitelisted = whitelistUrls.some((url: string) => response.config.url?.includes(url));

      // 检查是否在白名单中，并且重试次数小于 2
      if (isWhitelisted && (response.config.retryCount || 0) < 2) {
        response.config.retryCount = (response.config.retryCount || 0) + 1;

        // console.log(`Retrying request for URL: ${response.config.url}, Attempt: ${response.config.retryCount}`);

        // 在这里进行重试
        try {
          // 测试
          const config = response.config;
          if (typeof config.data === 'string') {
            try {
              config.data = JSON.parse(config.data);
              // config.data.UID = "1"
            } catch (e) {
              console.error('Failed to parse config.data:', e);
            }
            // } else {
            //   config.data = {
            //     ...(config.data || {}),
            //     UID: "1"
            //   };
          }
          await sleep(1000);
          const retryResponse = await instance.request(response.config);
          // console.log(`Retry successful for URL: ${response.config.url}`);
          return retryResponse; // 返回重试成功的响应
        } catch (retryError) {
          console.error(`Retry failed for URL: ${response.config.url}`, retryError);
          return Promise.reject(retryError); // 如果重试失败，返回错误
        }
      } else {
        console.log(`No retry for URL: ${response.config.url}. Either not whitelisted or max retries reached.`);
      }

      // when the backend response code is in `modalLogoutCodes`, it means the user will be logged out by displaying a modal
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (modalLogoutCodes.includes(responseCode) && !request.state.errMsgStack?.includes(response.data.msg)) {
        request.state.errMsgStack = [...(request.state.errMsgStack || []), response.data.msg];

        // prevent the user from refreshing the page
        window.addEventListener('beforeunload', handleLogout);
        // console.log(response.data.msg,'response.data.msg');
        window.$modal?.error({
          title: $t('common.error'),
          content: response.data.msg,
          okText: $t('common.confirm'),
          maskClosable: false,
          keyboard: false,
          onOk() {
            logoutAndCleanup();
          },
          onClose() {
            logoutAndCleanup();
          }
        });

        return null;
      }

      // when the backend response code is in `expiredTokenCodes`, it means the token is expired, and refresh token
      // the api `refreshToken` can not return error code in `expiredTokenCodes`, otherwise it will be a dead loop, should return `logoutCodes` or `modalLogoutCodes`
      // when the backend response code is in `expiredTokenCodes`, it means the token is expired, and refresh token
      // the api `refreshToken` can not return error code in `expiredTokenCodes`, otherwise it will be a dead loop, should return `logoutCodes` or `modalLogoutCodes`
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (expiredTokenCodes.includes(responseCode)) {
        const success = await handleExpiredRequest(request.state);
        if (success) {
          const Authorization = getAuthorization();
          Object.assign(response.config.headers, { Authorization });

          return instance.request(response.config) as Promise<AxiosResponse>;
        }
      }

      return null;
    },
    transformBackendResponse(response) {
      // console.log(response, "response====")
      // console.log(response.data.code, "response.data.code====")
      // console.log(response.data.data, "response.data.data====")
      // 只有200才返回数据
      // if (response.data.code === 200) {
      return response.data.data;
      // }
      // return [];
    },

    async onError(error) {
      // console.log(error, 'error');
      let message = error.message;
      const backendErrorCode = String(error.response?.data?.code || '');

      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.msg || message;
        console.log(error.response?.data?.code, 'error.response?.data?.code');
      }
      // the error message is displayed in the modal
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (modalLogoutCodes.includes(backendErrorCode)) {
        return;
      }

      function handleLogout() {
        store.dispatch(resetStore());
      }

      // when the token is expired, refresh token and retry request, so no need to show error message
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      // console.log(expiredTokenCodes, backendErrorCode, 'expiredTokenCodes');
      // console.log(expiredTokenCodes.includes(backendErrorCode), 'expiredTokenCodes.includes(backendErrorCode)');
      if (expiredTokenCodes.includes(backendErrorCode)) {
        // console.log('执行退出');
        handleLogout();
        window.location.href = '/login';

        return;
      }

      // 获取白名单 URL 列表
      // const whitelistUrls = import.meta.env.VITE_WHITELIST_URLS?.split(',') || [];
      const isWhitelisted = whitelistUrls.some((url: string) => error.config.url?.includes(url));

      // 检查是否在白名单中，并且重试次数小于 2
      if (isWhitelisted && (error.config.retryCount || 0) < 2) {
        error.config.retryCount = (error.config.retryCount || 0) + 1;

        console.log(`Retrying request for URL: ${error.config.url}, Attempt: ${error.config.retryCount}`);

        // 在这里进行重试
        try {
          // 测试
          const config = error.config;
          if (typeof config.data === 'string') {
            try {
              config.data = JSON.parse(config.data);
              // config.data.UID = "1"
            } catch (e) {
              console.error('Failed to parse config.data:', e);
            }
            // } else {
            //   config.data = {
            //     ...(config.data || {}),
            //     UID: "1"
            //   };
          }
          //  const retryResponse = await instance.request(error.config);
          //  console.log(`Retry successful for URL: ${response.config.url}`);
          //  return retryResponse; // 返回重试成功的响应
          await sleep(1000);
          return request(config);
        } catch (retryError) {
          console.error(`Retry failed for URL: ${error.config.url}`, retryError);
          return Promise.reject(retryError); // 如果重试失败，返回错误
        }
      } else {
        console.log(`No retry for URL: ${error.config.url}. Either not whitelisted or max retries reached.`);
      }

      showErrorMsg(request.state, message);
    }
  }
);

export const demoRequest = createRequest<App.Service.DemoResponse>(
  {
    baseURL: otherBaseURL.demo
  },
  {
    async onRequest(config) {
      const { headers } = config;

      // set token
      const token = localStg.get('token');
      const Authorization = token ? `Bearer ${token}` : null;
      Object.assign(headers, { Authorization });

      return config;
    },
    isBackendSuccess(response) {
      // when the backend response code is "200", it means the request is success
      // you can change this logic by yourself
      return response.data.status === '200';
    },
    async onBackendFail(_response) {
      // when the backend response code is not "200", it means the request is fail
      // for example: the token is expired, refresh token and retry request
    },
    transformBackendResponse(response) {
      return response.data.result;
    },
    onError(error) {
      // when the request is fail, you can show error message

      let message = error.message;

      // show backend error message
      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.message || message;
      }

      window.$message?.error(message);
    }
  }
);
