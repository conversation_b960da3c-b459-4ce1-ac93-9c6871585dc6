import { getIsLogin, selectUserInfo } from '@/store/slice/auth';
import { <PERSON>lapse, Button, Card, Col, Form, Input, Row, Tooltip, Tag, Statistic } from "antd";
import { useTranslation } from "react-i18next";
import { changePwd } from "@/service/api";
import { EditOutlined,TagOutlined,SyncOutlined } from '@ant-design/icons';
import { useSubmit } from 'react-router-dom';
import { useRoute } from '@sa/simple-router';
import { fetchLogout,UpdatePhone,UpdateEmail,UpdateOther,BindCoupon,CouponList } from '@/service/api/auth';
import { useLogin } from '@/hooks/common/login';
import CountUp from 'react-countup';
import ChangeContactModal from './model/ChangeContactModal';
import ChangeNameModal from './model/ChangeNameModal';
import CouponRedemptionModel from './model/CouponRedemptionModel';
import CouponListModal from './model/CouponListModal';
export function Component() {
    const userInfo = useAppSelector(selectUserInfo);
    const [formLoading, setFormLoading] = useState(false);
    // 余额刷新
    const [refreshing, setRefreshing] = useState(false);
    // 优惠券刷新
    const [refreshingCoupon, setRefreshingCoupon] = useState(false);
    // 充值弹窗
    const [showPayModal, setShowPayModal] = useState(false);
    // 优惠券列表弹窗
    const couponListRef = useRef<any>(null);
    // 是否已警告
    const [hasWarned, setHasWarned] = useState(false);
    const submit = useSubmit();
    const route = useRoute();
    const router = useRouterPush();
    const { t } = useTranslation();
    const { toGroupLogin } = useLogin();
    // ref修改手机号 or 邮箱
    const changeContactRef = useRef<any>(null);
    // ref兑换码
    const couponRedemptionRef = useRef<any>(null);
    const [changeContactType, setChangeContactType] = useState<'phone' | 'email'>('phone');
    // ref修改公司名称 or 用户名
    const changeNameRef = useRef<any>(null);


    // 修改手机号 or 邮箱
    const handleChangeContact = async (values: any) => {
        console.log(values)
        if(changeContactType === 'phone'){
            const res = await UpdatePhone(values)
            if(res && res.data){
                window.$message?.success('手机号修改成功')
                changeContactRef.current.close()
                handleRefreshBalance()
            }
        }else{
            const res = await UpdateEmail({
                email:values.phone,
                code:values.code,
                captchaKey:values.captchaKey,
                captchaValue:values.captchaValue
            })
            if(res && res.data){
                window.$message?.success('邮箱修改成功')
                changeContactRef.current.close()
                handleRefreshBalance()
            }
        }
    }

    // 修改公司名称 or 用户名
    const handleChangeName = async (values: any) => {
        console.log(values);
        // Implement the logic to update company name and username
        const res = await UpdateOther({
            company_name:values.company_name,
            nick_name:values.nick_name
        })
        if(res && res.data){
            window.$message?.success('名称修改成功');
            changeNameRef.current.close();
            handleRefreshBalance()
        }
    };

    // 打开修改手机号 or 邮箱弹窗
    const openChangeContact = (type: 'phone' | 'email') => {
        if (refreshing) return;
        setChangeContactType(type);
        changeContactRef.current.open();
    }

    // 打开修改公司名称 or 用户名弹窗
    const openChangeName = () => {
        if (refreshing) return;
        changeNameRef.current.open();
    }


    // 修改密码
    const handleSubmit = async (values: any) => {
        setFormLoading(true);
        console.log(values);
        const { oldpassword, newpassword, repassword } = values;
        const res = await changePwd({
            oldpassword,
            newpassword,
            repassword,
        })
        if (res && res.data) {
            logout()
        }
        setFormLoading(false);
    };

    function logout() {
        let needRedirect = false;
        if (!route.meta?.constant) needRedirect = true;
        fetchLogout().then(res => {
            submit({ redirectFullPath: route.fullPath, needRedirect }, { method: 'post', action: '/user_logout' });
            console.log(res);
        });
    }
    // 添加刷新余额的函数
    const handleRefreshBalance = async () => {
        if (refreshing) return;
        setRefreshing(true);
        setTimeout(() => {
            try {
                toGroupLogin(false, true)
            } finally {
                setRefreshing(false);
            }
        }, 500)
    };


    // 添加优惠券刷新函数
    const handleRefreshCoupon = async () => {
        if (refreshingCoupon) return;
        setRefreshingCoupon(true);
        const res = await CouponList({
            page:1,
            page_size:100
        })
        if(res && res.data){
            console.log(res.data)
        }
        setTimeout(() => {
            setRefreshingCoupon(false);
        }, 300);
    };

    // 添加格式化函数
    const formatter: StatisticProps['formatter'] = (value) => (
        <CountUp
            end={value as number}
            separator=","
            decimals={2}
            duration={1} // 动画持续时间
            preserveValue
        />
    );

    // 添加充值处理函数
    const handleRecharge = () => {
        console.log("充值")
        setShowPayModal(true);
        // router.routerPushByKey('recharge'); // 跳转到充值页面
    };
    const handleClosePayModal = () => {
        setShowPayModal(false);
        handleRefreshBalance()
    };
    useEffect(() => {
        handleRefreshBalance()
        // handleRefreshCoupon()
    }, []);
    useEffect(() => {
        if (userInfo?.OwnerID && !userInfo?.CompanyName && !hasWarned) {
            window.$message?.warning("请设置公司名称");
            changeNameRef.current.open();
            setHasWarned(true);
        }
    }, [userInfo, hasWarned]);

    // 兑换优惠券
    const handleCouponRedemption = async (values: any,captchaRef:any) => {
        console.log(values);
        const res = await BindCoupon({
            CouponCode:values.Code,
            VerifyCode:values.captchaKey,
            VerifyValue:values.captchaValue
        })
        if(res && res.data){
            window.$message?.success('兑换成功')
            couponRedemptionRef.current.close()
        }else{
            console.log("captchaRef.current")
            captchaRef.current.refreshCaptcha()
        }
    }
    return (
        <>
            {showPayModal && <PayModel open={showPayModal} onCancel={handleClosePayModal} />}
            {/* 修改手机号 or 邮箱 */}
            <ChangeContactModal  initialValues={{
                phone: userInfo.Phone,
                email: userInfo.Email
            }} ref={changeContactRef} onSubmit={handleChangeContact} type={changeContactType} />
            {/* 修改公司名称 or 用户名 */}

            <CouponRedemptionModel ref={couponRedemptionRef} onSubmit={handleCouponRedemption} />
            
            <ChangeNameModal initialValues={{ company_name: userInfo.CompanyName, nick_name: userInfo.NickName }} ref={changeNameRef} onSubmit={handleChangeName} />
            
            {/* 优惠券列表 */}
            <CouponListModal ref={couponListRef} />

            <Card className="m-1" title="账号信息">
                <div className="flex items-center">
                    {/* 左侧头像部分 */}
                    <div className="w-[120px] flex-shrink-0 mr-8 flex justify-center">
                        <SoybeanAvatar size={100} />
                    </div>

                    {/* 右侧信息部分 */}
                    <div className="flex-1">
                        {/* 基本信息部分 */}
                        <div className="">
                            <Row gutter={24}>
                                <Col span={6}>
                                    <div className="mb-4">
                                        <div className="text-gray-500 text-sm mb-1">公司名称
                                           
                                        </div>
                                        <div className="flex items-center font-500">
                                        <span className={`${!userInfo?.CompanyName ? 'text-yellow-500' : ''}`}>{!userInfo?.CompanyName ? "请设置公司名称" : ""}</span>
                                        {userInfo.CompanyName ?? "--"}
                                            <EditOutlined onClick={() => openChangeName()} className='ml-1 text-primary cursor-pointer' />

                                            {/* {userInfo.IsMainAccount && ( */}
                                                {/* <Tag color="#f50" className="ml-2">主账号</Tag> */}
                                            {/* // )} */}
                                        </div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div className="mb-4">
                                        <div className="text-gray-500 text-sm mb-1">用户名</div>
                                        <div className="flex items-center font-500">
                                        {/* {userInfo.NickName ?? "--"} */}
                                        <span >{userInfo?.NickName || "--"}</span>
                                            <EditOutlined onClick={() => openChangeName()} className='ml-1 text-primary cursor-pointer' />

                                            {/* {userInfo.IsMainAccount && ( */}
                                                {/* <Tag color="#f50" className="ml-2">主账号</Tag> */}
                                            {/* // )} */}
                                        </div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div className="mb-4">
                                        <div className="text-gray-500 text-sm mb-1">手机号码</div>
                                        <div className='flex items-center font-500'>
                                            <span >{userInfo?.Phone || "--"}</span>
                                            <EditOutlined onClick={() => openChangeContact('phone')} className='ml-1 text-primary cursor-pointer' />
                                        </div>
                                    </div>
                                </Col>
                                <Col span={6}>
                                    <div className="mb-4">
                                        <div className="text-gray-500 text-sm mb-1">邮箱账号</div>
                                        <div className='flex items-center font-500'>
                                            <span >{userInfo?.Email || "--"}</span>
                                            <EditOutlined onClick={() => openChangeContact('email')} className='ml-1 text-primary cursor-pointer' />
                                        </div>
                                    </div>
                                </Col>



                                {/* 添加总余额显示 */}
                                <Col span={6}>
                                    <div className="mb-4">
                                        <div className="text-gray-500 text-sm mb-1 flex items-center">
                                        公司账户余额
                                            <Tooltip title="刷新余额">
                                                <SyncOutlined
                                                    className={`ml-2 cursor-pointer hover:text-primary ${refreshing ? 'animate-spin' : ''}`}
                                                    onClick={handleRefreshBalance}
                                                />
                                            </Tooltip>
                                            <Button
                                                    type="link"
                                                    size="small"
                                                    onClick={handleRecharge}
                                                    className="ml-2"
                                                >
                                                    充值
                                                </Button>
                                        </div>
                                        <ASpace align="center">
                                            <Statistic
                                                value={userInfo.SumBalance ?? 0}
                                                precision={2}
                                                formatter={formatter}
                                                valueStyle={{
                                                    color: '#e1ab51',
                                                    fontSize: '26px',
                                                    fontWeight: 500
                                                }}
                                                prefix="¥"
                                            />
                                            <div>
                                               
                                                {/* <Button>

                                            </Button> */}
                                            </div>
                                        </ASpace>
                                    </div>
                                </Col>


                                <Col span={6}>
                                    <div>
                                         <div className="text-gray-500 text-sm mb-1 flex items-center">
                                        优惠券
                                            {/* <Tooltip title="刷新余额">
                                                <SyncOutlined
                                                    className={`ml-2 cursor-pointer hover:text-primary ${refreshingCoupon ? 'animate-spin' : ''}`}
                                                    onClick={handleRefreshCoupon}
                                                />
                                            </Tooltip> */}
                                            <Button
                                                    type="link"
                                                    size="small"
                                                    
                                                    onClick={() => couponListRef.current.open()}
                                                  
                                                    // icon={}
                                                >
                                                    查看
                                                </Button>
                                        </div>
                                        <ASpace align="center">
                                        <div>
                                            
                                                <Button
                                                    type="link"
                                                    size="small"
                                                    // className="ml-2"
                                                    onClick={() => couponRedemptionRef.current.open()}
                                                    // style={{marginTop:'10px',marginLeft:'-6px'}}
                                                >
                                                  兑换
                                                </Button>
                                            </div>
                                            <Statistic
                                                value={0}
                                                precision={2}
                                                formatter={formatter}
                                                valueStyle={{
                                                    color: '#e1ab51',
                                                    fontSize: '26px',
                                                    fontWeight: 500,
                                                    visibility: 'hidden'
                                                }}
                                                // prefix="¥"
                                            />
                                            
                                        </ASpace>
                                    </div>
                                </Col>

                            </Row>
                        </div>
                    </div>
                </div>
            </Card>
            <div className="mt-4">
                <Card title="安全设置" bordered={false}>
                    <Collapse
                        bordered={false}
                        defaultActiveKey={['1']} // 默认展开第一项
                        className="bg-white"
                        items={[
                            {
                                key: '1',
                                label: (
                                    <div className="flex justify-between w-full">
                                        <span className="text-base">修改密码</span>
                                        <span className="flex items-center text-gray-400 text-xs truncate max-w-[70%]">
                                            安全性高的密码可以使帐号更安全。建议您定期更换密码，设置一个包含字母和数字且长度超过8-20位的密码。
                                        </span>
                                    </div>
                                ),
                                children: (
                                    <Form
                                        layout="vertical"
                                        onFinish={handleSubmit}
                                        className="max-w-[500px] pl-6"
                                        disabled={formLoading}
                                    >
                                        <Form.Item
                                            label={t("page.manage.user.oldPassword")}
                                            name="oldpassword"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: t("page.manage.user.form.oldPasswordPlaceholder"),
                                                },
                                                {
                                                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
                                                    message: t("form.pwd.invalid"),
                                                },
                                            ]}
                                        >
                                            <Input.Password placeholder="请输入最短8位，包含大小写字母、数字的旧密码" />
                                        </Form.Item>

                                        <Form.Item
                                            label={t("page.manage.user.newPassword")}
                                            name="newpassword"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: t("page.manage.user.form.newPasswordPlaceholder"),
                                                },
                                                {
                                                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
                                                    message: t("form.pwd.invalid"),
                                                },
                                                ({ getFieldValue }) => ({
                                                    validator(_, value) {
                                                        if (!value || getFieldValue('oldpassword') !== value) {
                                                            return Promise.resolve();
                                                        }
                                                        return Promise.reject(new Error(t("form.pwd.sameAsOld")));
                                                    },
                                                }),
                                            ]}
                                        >
                                            <Input.Password placeholder="请输入最短8位，包含大小写字母、数字的新密码" />
                                        </Form.Item>

                                        <Form.Item
                                            label={t("page.manage.user.confirmPassword")}
                                            name="repassword"
                                            dependencies={['newpassword']}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: t("page.manage.user.form.confirmPasswordPlaceholder"),
                                                },
                                                {
                                                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
                                                    message: t("form.pwd.invalid"),
                                                },
                                                ({ getFieldValue }) => ({
                                                    validator(_, value) {
                                                        if (!value || getFieldValue('newpassword') === value) {
                                                            return Promise.resolve();
                                                        }
                                                        return Promise.reject(new Error(t("form.pwd.notMatch")));
                                                    },
                                                }),
                                            ]}
                                        >
                                            <Input.Password placeholder="请输入" />
                                        </Form.Item>

                                        <Form.Item>
                                            <Button type="primary" htmlType="submit">
                                                确认修改
                                            </Button>
                                        </Form.Item>
                                    </Form>
                                ),
                            },
                        ]}
                    />
                </Card>
            </div>
        </>
    );
}