import {
    Typo<PERSON>,
    Modal
} from "antd";
import authEn01 from '@/assets/imgs/auth-en01.png';
import authEn02 from '@/assets/imgs/auth-en02.png';
import type { TableProps } from 'antd';
import { SyncOutlined, InfoCircleFilled, CloseCircleOutlined, DownOutlined, EditOutlined, DeleteOutlined, InfoCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Icon } from '@iconify/react';
import { <PERSON>A<PERSON>, <PERSON>A<PERSON>, OuthList, DelSPOuth, DelADOuth, ShopAdd, ShopChange, ShopDelete, switchUser } from "@/service/api";
import { useTableScroll } from '@/hooks/common/table';
import { getContinentsName } from "@/components/weekly-vchart/chart";
import { selectUserInfo } from '@/store/slice/auth';
import { useLogin } from '@/hooks/common/login';
import { useSearchParams } from 'react-router-dom';
// import ModelAuthorization from "@/components/model-authorization";
// import AddShop from "./model/AddShop";
const ModelAuthorization = lazy(() => import("./model/model-authorization/index"));
const AddShop = lazy(() => import("./model/AddShop"));
export function Component() {
    const { Content } = ALayout;
    const { Panel } = ACollapse;
    const { t } = useTranslation();
    const { rolesAuth } = useAuth();
    const [searchParams] = useSearchParams();
    const redirectParam = searchParams.get("new");
    const { Title, Text } = Typography;
    const [defaultRegion, setDefaultRegion] = useState('NA');
    const [visible, setVisible] = useState(false);
    const [addShopVisible, setAddShopVisible] = useState(false);
    const [loading, setLoading] = useState(true);
    const { tableWrapperRef, scrollConfig } = useTableScroll();
    const userInfo = useAppSelector(selectUserInfo);
    const [authRecord, setAuthRecord] = useState({});
    const [isAdmin, setIsAdmin] = useState(false);
    const nav = useNavigate()
    // 选中行
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [checkStrictly, setCheckStrictly] = useState(false);


    // 弹窗类型
    const [ShopModalRecord, setShopModalRecord] = useState({
        type: 'add',
        ShopName: '',
        IsDistributor: 0,
    });

    const { toGroupLogin, toAuthLogin } = useLogin();

    const raw = [
        {
            key: 1,
            shop: 'NA',
            isShow: false,
            children: [
                {
                    key: 11,
                    shop: '',
                    country: 'US',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: ''
                },
                {
                    key: 12,
                    shop: '',
                    country: 'CA',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: ''
                },
                {
                    key: 13,
                    shop: '',
                    country: 'MX',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: ''
                },
                {
                    key: 14,
                    shop: '',
                    country: 'BR',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: ''
                },
            ],
        },
        {
            key: 2,
            shop: 'EU',
            isShow: false,
            children: [
                {
                    key: 20,
                    shop: '',
                    country: 'UK',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 21,
                    shop: '',
                    country: 'FR',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 22,
                    shop: '',
                    country: 'DE',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 23,
                    shop: '',
                    country: 'IT',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 24,
                    shop: '',
                    country: 'ES',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 25,
                    shop: '',
                    country: 'NL',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 26,
                    shop: '',
                    country: 'SE',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 27,
                    shop: '',
                    country: 'PL',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 28,
                    shop: '',
                    country: 'BE',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                }, {
                    key: 29,
                    shop: '',
                    country: 'IN',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                }, {
                    key: 30,
                    shop: '',
                    country: 'TR',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                },
                {
                    key: 31,
                    shop: '',
                    country: 'IE',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                }
            ],
        },
        {
            key: 3,
            shop: 'JP',
            isShow: false,
            children: [
                {
                    key: 39,
                    shop: '',
                    country: 'JP',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                }
            ],
        },
        {
            key: 4,
            shop: 'SG',
            isShow: false,
            children: [
                {
                    key: 41,
                    shop: '',
                    country: 'SG',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                }
            ],
        },
        {
            key: 5,
            shop: 'AU',
            isShow: false,
            children: [
                {
                    key: 51,
                    shop: '',
                    country: 'AU',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                }
            ],
        },
        // AE SA
        {
            key: 6,
            shop: 'SA',
            isShow: false,
            children: [
                {
                    key: 61,
                    shop: '',
                    country: 'SA',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                }
            ],
        },
        {
            key: 7,
            shop: 'AE',
            isShow: false,
            children: [
                {
                    key: 71,
                    shop: '',
                    country: 'AE',
                    advertise: 0,
                    shopauth: 0,
                    shopauthtime: '',
                }
            ],
        },
    ];
    const ad_raw = [
        {
            key: 1,
            value: 'NA',
            label: '北美',
            children: [
                { value: 'US', label: '美国' },
                { value: 'CA', label: '加拿大' },
                { value: 'MX', label: '墨西哥' },
                { value: 'BR', label: '巴西' }
            ]
        },
        {
            key: 2,
            value: 'EU',
            label: '欧洲',
            children: [
                { value: 'UK', label: '英国' },
                { value: 'FR', label: '法国' },
                { value: 'DE', label: '德国' },
                { value: 'IT', label: '意大利' },
                { value: 'ES', label: '西班牙' },
                { value: 'NL', label: '荷兰' },
                { value: 'SE', label: '瑞典' },
                { value: 'PL', label: '波兰' },
                { value: 'BE', label: '比利时' },
                { value: 'IN', label: '印度' },
                { value: 'TR', label: '土耳其' },
                { value: 'ZA', label: '南非' },
                { value: 'EG', label: '埃及' },
                { value: 'IE', label: '爱尔兰' },
                { value: 'SA', label: '沙特阿拉伯' },
                { value: 'AE', label: '阿联酋' },

            ]
        },
        {
            key: 3,
            value: 'FE',
            label: '远东',
            children: [
                { value: 'JP', label: '日本' },
                { value: 'SG', label: '新加坡' },
                { value: 'AU', label: '澳大利亚' }
            ]
        }
    ]
    const columns = [
        {
            key: 'shop',
            // title: t('page.setting.auth.shop'),
            title: "",
            dataIndex: 'shop',
            // width: 25,
            // minWidth: 500,
            render: (text: any, record: any) => {

                // 并且key转换为数字是整数
                if (record.isParent) {
                    return (
                        <div className="flex items-center gap-2 whitespace-nowrap absolute z-9 left-10" style={{ top: '52%', transform: 'translateY(-50%)' }}>
                            <p className="text-sm font-500 text-gray-500 flex items-center">
                                {t('page.table.columns.shopname')}：<span className="text-black">{text}</span>
                                <span className="text-primary cursor-pointer ml-2" onClick={(e) => {
                                    // console.log(record, "record===");
                                    e.stopPropagation();
                                    showModel(record)
                                }}>{t('page.setting.auth.shopauth')}</span>
                            </p>

                        </div>
                    );
                } else if (!record.isSon) {
                    return (
                        <div className="flex items-center gap-2 whitespace-nowrap absolute z-9 left-14">

                            <Icon icon={getContinentsName(text)?.icon} width={24} height={24} />
                            <p className="text-sm font-500 ">{t(`page.setting.country.${text}`)}</p>
                        </div>
                    );
                }
            }
        },
        {
            key: 'country',
            title: t('page.setting.auth.country'),
            dataIndex: 'country',
            render: (text: any, record: any, index: number) => {
                return (
                    <div >
                        {/* {console.log(record,index)} */}

                        {
                            !record.children && !record.isParent && (
                                <div className="flex items-center" >
                                    <Icon
                                        className="mr-1"
                                        icon={`circle-flags:${text?.toLowerCase()}`}
                                        width={25}
                                        height={25}
                                    />
                                    {
                                        record?.isSon && record?.shopauth == 1 && record?.advertise == 1 ? (
                                            // 下划线 颜色为pr
                                            <a className="whitespace-nowrap underline cursor-pointer text-primary" onClick={(e) => {
                                                e.stopPropagation();
                                                goToCountry(record)
                                            }}>{t(`page.setting.country.${text}`)}</a>

                                        ) : (
                                            <span className="whitespace-nowrap">{t(`page.setting.country.${text}`)}</span>
                                        )
                                    }

                                    {/* 数据采集异常 */
                                        record.ReportNotEnglish ? (
                                            <APopover
                                                placement="right"
                                                content={
                                                    <div className="p-4 w-[520px]">
                                                        <div className="mb-4">
                                                            <p className="font-medium mb-2 text-base">{t('page.setting.auth.reportLanguage.reportfailed')}</p>
                                                            <p className="text-gray-600 mb-2">{t('page.setting.auth.reportLanguage.reportfaileddesc')}
                                                                <span className="font-bold text-error text-lg">{t(`page.setting.country.${text}`)}</span>
                                                                {t('page.setting.auth.reportLanguage.reportfailed1')}</p>
                                                            <p className="text-gray-600">{t('page.setting.auth.reportLanguage.reportfailed2')}</p>
                                                            <div className="bg-[#F5F5F5] p-3 mt-2 rounded text-gray-600">
                                                                {t('page.setting.auth.reportLanguage.reportfailed3')}
                                                                <span className="mx-1 text-gray-400">/</span>
                                                                {t('page.setting.auth.reportLanguage.reportfailed4')}
                                                                <span className="mx-1 text-gray-400">/</span>
                                                                {t('page.setting.auth.reportLanguage.reportfailed5')}
                                                                <span className="mx-1 text-gray-400">/</span>
                                                                {t('page.setting.auth.reportLanguage.reportfailed6')}
                                                                <span className="mx-1 text-gray-400">/</span>
                                                                {t('page.setting.auth.reportLanguage.reportfailed7')}
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <p className="font-medium mb-2 text-base">{t('page.setting.auth.reportLanguage.reportfailed8')}</p>
                                                            <AImage
                                                                src={authEn01}
                                                                className="w-full rounded"
                                                                alt={t('page.setting.auth.reportLanguage.example1')}
                                                                preview={{
                                                                    mask: t('page.setting.auth.reportLanguage.clicktoview')
                                                                }}
                                                            />
                                                            <AImage
                                                                src={authEn02}
                                                                className="w-full rounded"
                                                                alt={t('page.setting.auth.reportLanguage.example2')}
                                                                preview={{
                                                                    mask: t('page.setting.auth.reportLanguage.clicktoview')
                                                                }}
                                                            />
                                                        </div>
                                                    </div>
                                                }
                                                trigger="hover"
                                            >
                                                <div className="flex items-center cursor-pointer  ml-1">
                                                    <div className="relative flex items-center">
                                                        <Icon
                                                            icon="line-md:bell-alert-loop"
                                                            className="text-lg text-error"
                                                        />
                                                        <span className="absolute -top-1 -right-1 w-2 h-2 bg-error rounded-full animate-ping" />
                                                    </div>
                                                    <span className="text-error text-xs font-medium whitespace-nowrap ml-1">{t('common.error')}</span>
                                                </div>

                                            </APopover>
                                        ) : null
                                    }
                                </div>
                            )
                        }
                    </div>
                );
            },
        },

        {
            key: 'shopauth',
            title: t('page.setting.auth.shopauth'),
            dataIndex: 'shopauth',
            align: 'center',
            // width: 100,
            render: (text: any, record: any, index: number) => {
                return (
                    <div className='flex items-center justify-center'>
                        {
                            text === 1 &&
                            <Icon icon='flat-color-icons:checkmark' className='text-primary text-2xl'></Icon>
                        }
                        {
                            text === 0 &&
                            <p className="text-primary cursor-pointer" onClick={() => showModel(record)}>{t('page.setting.authmodel.auth')}</p>
                        }
                    </div>
                );
            },
        },
        {
            key: 'advertise',
            title: t('page.setting.auth.advertise'),
            dataIndex: 'advertise',
            align: 'center',
            // width: 100,
            render: (text: any, record: any, index: number) => {
                return (
                    <div className='flex items-center justify-center'>
                        {
                            record.shopauth === 0 ?

                                <Text className="text-primary cursor-pointer" onClick={() => adAuth(record)}>{t('page.setting.authmodel.auth')}</Text>
                                : <>
                                    {
                                        text === 1 &&
                                        <Icon icon='flat-color-icons:checkmark' className='text-primary text-2xl'></Icon>
                                        // <p className="text-primary cursor-pointer">{t('page.setting.authmodel.authsuccess')}</p>
                                    }
                                    {
                                        text === 0 &&
                                        <p className="text-primary cursor-pointer" onClick={() => adAuth(record)}>{t('page.setting.authmodel.auth')}</p>
                                    }
                                </>
                        }
                    </div>
                );
            },
        },
        // {
        //     key: 'shopauthtime',
        //     title: t('page.setting.auth.shopauthtime'),
        //     dataIndex: 'shopauthtime',
        //     align: 'center',
        //     ellipsis: true,

        // },
        {
            key: 'adServiceActive',
            title: t('page.setting.auth.advertisestatus'),
            dataIndex: 'adServiceActive',
            align: 'center',
            // width: 160,
            render: (text: any, record: any) => {
                const currentDate = new Date();
                const endDate = new Date(record.adServiceEndDatetime);
                const diffTime = endDate.getTime() - currentDate.getTime();
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                // if (text === 1) {
                //     console.log(record.adServiceEndDatetime, "record.adServiceEndDatetime===");
                //     console.log(currentDate, "currentDate===");
                //     console.log(diffTime, "diffTime===");
                //     console.log(diffDays, "diffDays===");
                // }



                if (diffDays < -15) {
                    return <ATag color="danger">{t('page.setting.auth.cancel')}</ATag>;
                } else if (diffDays <= 0) {
                    return <ATag color="warning">{t('page.setting.auth.expired')}（{15 + diffDays} {t('page.setting.auth.remove')}）</ATag>;
                } else if (text === 1) {
                    // return <ATag color="success">已激活</ATag>;
                    return <ATag color="success">{t('page.setting.auth.active')}（
                        {/* 时间小于7天 黄色 小于三天 红色 */}
                        {diffDays <= 3 ? <span className="text-error font-bold text-base mx-1">{diffDays}</span> : diffDays <= 7 ? <span className="text-yellow-500 font-bold text-base mx-1">{diffDays}</span> : diffDays}
                        {/* {diffDays} */}
                        {t('page.setting.auth.daystodate')}）</ATag>;
                } else if (text === 0) {
                    return <ATag color="default">{t('page.setting.auth.inactive')}</ATag>;
                } else {
                    return null;
                }
            }
        },
        {
            key: 'adServiceEndDatetime',
            title: t('page.table.columns.serviceexpire'),
            dataIndex: 'adServiceEndDatetime',
            align: 'center',
            // width: 160,
        },
        {
            key: 'operation',
            title: t('page.setting.auth.operation'),
            // width: 200,
            dataIndex: 'operation',
            align: 'center',
            render: (text: any, record: any) => {

                if (record.isParent) {
                    // 展示一个删除
                    return <>
                        <EditOutlined
                            className="cursor-pointer text-primary hover:text-blue-600 mr-4"
                            onClick={(e) => {
                                if (loading) return;
                                e.stopPropagation();
                                setShopModalRecord({
                                    ...record,
                                    ShopName: record.shop,
                                    IsDistributor: 0,
                                    type: 'edit'
                                });
                                console.log(ShopModalRecord);
                                setAddShopVisible(true);
                            }}
                        />
                        {isAdmin &&
                            <DeleteOutlined
                                className="cursor-pointer text-red-500 hover:text-red-600"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    Modal.confirm({
                                        title: t('page.setting.auth.confirmdelete'),
                                        content:
                                            <div className="py-2">
                                                {t('page.setting.auth.confirmdelete1')}
                                                <span className="font-bold text-base text-red-500 mx-1">{record.shop}</span>
                                                <p>  {t('page.setting.auth.confirmdelete2')}</p>
                                            </div>,
                                        okText: t('page.login.common.confirm'),
                                        okType: 'danger',
                                        cancelText: t('page.login.common.back'),
                                        onOk: () => handleDeleteShop(record),
                                    });
                                    // handleDeleteShop(record);
                                }}
                            />
                        }
                    </>
                }

                return (
                    <div className="flex items-center justify-center">
                        {
                            record?.isSon && <p className="text-[12px] mx-2 text-primary cursor-pointer" onClick={(e) => {
                                e.stopPropagation();
                                handleBatchActivateShop(record)
                            }}>
                                {record?.isSon && record.adServiceActive ? (
                                    <div className="flex items-center">
                                        <Icon icon='mdi:close-circle-multiple-outline' className="text-base mr-1"></Icon>
                                        {t('page.setting.auth.Unsubscribe')}
                                    </div>
                                ) : (
                                    // 激活服务使用目标图标
                                    <div className="flex items-center">
                                        {/* <AimOutlined className="" /> */}
                                        <Icon icon='mingcute:ai-line' className="text-base mr-1"></Icon>
                                        {/* {t('page.setting.auth.advertisingserviceactivation')} */}

                                        {t('page.setting.auth.Subscribe')}
                                    </div>
                                )}
                            </p>
                        }
                        {subDropdown(record)}
                    </div>
                );
            },
        },
    ];
    const [raws, setRaw] = useState<any[]>([]);
    // 获取父节点
    const getParent = (record: any) => {
        let region;
        let market = [record.country];
        const parent: any = raw.find(item => item.children.some((child: any) => child.country === record.country));
        console.log(parent, "parent===");
        region = parent.shop;
        return { region, market };
    }

    // 获取子节点
    const getChildren = (record: any) => {
        let region, market;
        region = record.shop;
        if (record.children && record.children.length > 0) {
            market = record.children.map((item: any) => item.country);
        }
        return { region, market };
    }

    const pauseSyncAndUpdateAuth = useCallback(async (record: any, type: string, auth_type = "SP") => {
        setLoading(true);
        // console.log(record, "record===");
        // console.log(type, "type===");
        // console.log(auth_type, "auth_type===");

        // if (loading) return;
        // console.log(record);
        // return
        let { region, market } = record.children ? getChildren(record) : getParent(record);
        // console.log(region, market);
        // return
        // console.log(type === 'pause' ? 'Pausing sync' : 'Updating auth', region, market);
        // API call logic here

        callApi(record, region, market, type, auth_type);
    }, []);
    const subDropdown = useCallback((record: any) => {
        const items: MenuProps['items'] = [
            // {
            //     label: (
            //         <span onClick={(e) => {
            //             e.stopPropagation();
            //             handleBatchActivateShop(record)
            //         }}>
            //             <AimOutlined className="mr-1" />

            //             {/* {
            //                 record?.children.lenght>0?
            //             } */}
            //             {record?.isSon && record.adServiceActive ? "延长服务时间" : "广告服务激活"}
            //         </span>
            //     ),
            //     key: '0',
            //     // icon: ,
            // },
            record.isSon ? {
                label: (
                    <span onClick={(e) => {
                        e.stopPropagation();
                        console.log(record, "record===");
                        // return
                        // 获取key中的最后一个
                        const text = record.key.split('-')[record.key.split('-').length - 1]
                        const region = t(`page.setting.country.${text}`)
                        const country = t(`page.setting.country.${text}`)
                        Modal.confirm({
                            title: t('page.setting.auth.cancelauth'),
                            content: <div className="py-4">

                                {/* 1.此操作将暂停指定站点（或者地区）和XXX共用一个授权，取消该站点授权会导致XXXXXXX授权一并取消，请谨慎操作。 */}
                                {t('page.setting.auth.cancelauthdesc')}
                                <span className="font-bold text-red-500">{region ? region : country} {region ? t('page.setting.authmodel.region') : t('page.setting.auth.site')}</span>
                                <p>{t('page.setting.auth.cancelauthdesc1')}</p>
                            </div>,

                            onOk: () => pauseSyncAndUpdateAuth(record, 'pause'),
                            okText: t('page.login.common.confirm'),
                            okType: "danger",
                            cancelText: t('page.login.common.back'),
                        });
                    }}>
                        <CloseCircleOutlined className="mr-1" />
                        {/* {t('page.setting.auth.pauseauth')} */}
                        {t('page.setting.auth.cancelauth')}
                    </span>
                ),
                key: '1',
                danger: true,
                // icon: <CloseCircleOutlined />,
            } : null,
            {
                label: (
                    <span onClick={(e) => {
                        e.stopPropagation();
                        showModel(record)
                    }}>
                        <SyncOutlined className="mr-1 text-primary" />
                        {t('common.update')} {t('page.setting.auth.shopauth')}
                    </span>
                ),
                key: '2',
                // icon: <SyncOutlined />,
            },
            {
                label: (
                    <span className="flex items-center" onClick={(e) => {
                        e.stopPropagation();
                        adAuth(record)
                    }}>
                        <Icon icon="mingcute:ad-circle-line" className="text-primary text-lg ml-[-2px] mr-[2px]" />
                        {t('common.update')} {t('page.setting.auth.advertise')}
                    </span>
                ),
                key: '3',
                // icon: <SyncOutlined />,
            },
        ];

        return (
            <ADropdown menu={{ items }} trigger={['click']} arrow={{ pointAtCenter: true }} placement="bottomRight">
                <p className="cursor-pointer flex items-center text-[12px]" onClick={(e) => e.stopPropagation()}>
                    {t('page.setting.auth.more')}
                    {/* {record?.isSon ? t('page.setting.auth.more') : t('page.setting.auth.operation')} */}
                    <DownOutlined />
                </p>
            </ADropdown>
        );
    }, [t, pauseSyncAndUpdateAuth]);

    // sp api 授权
    const showModel = (record: any) => {
        let region, market;
        if (record.isParent) {
            region = "NA"
        } else {
            ({ region, market } = record.children ? getChildren(record) : getParent(record));
        }
        setVisible(true);
        setAuthRecord(record);
        setDefaultRegion(region);
    }
    // 调用api
    const callApi = async (record: any, region: string, market: any, type: string, auth_type: string) => {
        if (type === 'pause') {
            // console.log('暂停同步', region, market, auth_type);
            delAuth(record, region, market);
        } else if (type === 'update') {
            // console.log('更新授权', region, market);
            handleSPAuth(region, market, auth_type, record);
        }
    }
    // 删除授权
    const delAuth = async (record: any, region: string, market: any) => {
        setLoading(true);
        const adRegion = ad_raw.find(item =>
            item.children.some(child => child.value === record.country)
        )?.value;


        // return
        try {
            const UID = record.key.split('-')[0];
            const req_data = {
                "AreaCode": region,
                "CountryCode": market.length > 1 ? "" : market.join(','),
                "UID": Number(UID)
            }
            if (record.shop) {
                // 判断 record.children 中的 shopauth 有没有 1
                if (record.children.some((item: any) => item.shopauth === 1)) {
                    const SPres = await DelSPOuth(req_data);
                    // if (SPres && SPres.data) {
                    //     window.$message?.success("店铺授权暂停成功");
                    // }
                }
                // 判断 record.children 中的 advertise 是否都为1
                // if (record.children.every((item: any) => item.advertise === 1)) {
                //     // console.log(adRegion, "adRegion===");
                //     // return
                //     const ADres = await DelADOuth({ ...req_data, AreaCode: adRegion });
                //     if (ADres && ADres.data) {
                //         window.$message?.success("广告授权暂停成功");
                //     }
                // }
            } else {
                if (record.shopauth) {
                    const SPres = await DelSPOuth(req_data);
                    // if (SPres && SPres.data) {
                    //     window.$message?.success("店铺授权暂停成功");
                    // }
                }
                // if (record.advertise) {
                //     // console.log(adRegion, "adRegion===");
                //     // return
                //     const ADres = await DelADOuth({ ...req_data, AreaCode: adRegion });
                //     if (ADres && ADres.data) {
                //         window.$message?.success("广告授权暂停成功");
                //     }
                // }
            }
        } catch (error) {
            console.error('Failed to delete authorization data:', error);
        }
        getAuthData();
    }
    const adAuth = (record: any) => {
        console.log(record, "record===");
        if (!record.shopauth) {
            window.$notification?.warning({
                message: t('page.setting.auth.shopnotauthorized'),
                description: t('page.setting.auth.shopnotauthorizeddesc'),
            });
            return;
        }
        let parent;
        ad_raw.forEach(item => {
            // console.log(item, "item===");
            item.children.forEach(child => {
                // console.log(child, "child===");
                if (child.value == record.country) {
                    console.log(item);
                    parent = {
                        region: item.label,
                        value: item.value
                    };
                }
            })
        })
        // console.log(parent,"parent===");
        // return
        custom(parent, record);
        // handleSPAuth(parent, "", 'AD');
    }
    const custom = (parent: any, record: any) => {
        console.log(record, parent, "record===");
        Modal.warning({
            // ${t('page.setting.auth.advertiseauth')}
            title: <div className="flex items-center">
                {/* <span className="font-500 text-primary">店铺：{record.parent_shop}-</span> */}
                <span>{t('page.setting.auth.advertiseauthconfirm')}</span>
            </div>,
            content: (
                <div className="py-4">
                    <p className="flex items-center mb-2">
                        <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">{t('page.setting.auth.shop')}:</span>
                        <span className="text-primary font-medium text-lg">{record.parent_shop}</span>
                    </p>
                    <p className="flex items-center mb-3">
                        <span className="inline-block w-[70px] text-gray-600 flex-shrink-0">{t('page.setting.authmodel.region')}:</span>
                        <span className="text-primary font-medium text-lg">{t(`page.setting.country.${parent.value}`)}</span>
                    </p>
                    <p className="text-[13px] text-gray-600 bg-[#e6f7ff] border border-[#91d5ff] px-4 py-3 rounded">
                        {t('page.setting.auth.advertiseauthdesc')}
                    </p>
                </div>
            ),

            // icon: <IconSend />,
            // cancelButtonProps: { theme: 'borderless' },
            // okButtonProps: { theme: 'borderless' },
            // centered:true,
            // hasCancel: false,
            closable: true,
            okText: `${t('page.setting.auth.advertiseauthbtn')}`,
            onOk: () => {
                handleSPAuth(parent.value, "", 'AD', record);
            },
            onCancel: () => {
                getAuthData();
            }
        });
    }
    const handleSPAuth = async (region: string, market: any, type = 'SP', record: any) => {
        // console.log(region,market,type);
        const UID = record.key.split('-')[0];
        // return
        let Region = region;
        if (type == "AD") {
            // 查询ad_raw
            Region = ad_raw.find(item =>
                item.children.some(child => child.value === record.country)
            )?.value;
            // console.log(adRegion, "adRegion===");
        }

        // return

        setLoading(true);
        const req_data = {
            "AreaCode": Region,
            "CountryCode": market,
            "ReAuth": 1,
            // 转为数字
            "UID": Number(UID)
        }
        try {
            const res = await type === 'SP' ? await SPAuth(req_data) : await ADAuth(req_data);
            if (res && res.data) {
                window.open(res.data.url, '_blank');
            }
        } catch (error) {
            console.error('Failed to authorize:', error);
        }
        setTimeout(() => {
            getAuthData();
        }, 300);
    }
    // 获取授权数据列表
    const getAuthData = async () => {
        initValue();
        setLoading(true);
        try {
            //  res;
            // if (userInfo.OwnerFlag == 0) {
            let res = await OuthList();
            // }

            if (res && res.data) {
                let shopList: any[] = [];

                // 遍历每个店铺
                Object.keys(res.data).forEach((shopId: string) => {
                    const shopData = res.data[shopId];
                    let shopItem = {
                        key: shopData.ID.toString(),
                        shop: shopData.ShopName,
                        isParent: true,
                        children: [] as any[]
                    };

                    // 获取该店铺下所有的地区（合并 sp 和 ad 的地区）
                    const regions = new Set([
                        ...Object.keys(shopData.sp)
                        // ...Object.keys(shopData.ad)
                    ]);
                    // console.log(regions, "regions===");


                    // 遍历地区
                    regions.forEach((region: string) => {
                        let regionItem = {
                            key: `${shopId}-${region}`,
                            shop: region,
                            parent_shop: shopData.ShopName,
                            children: [] as any[]
                        };

                        // 根据地区获取对应的国家列表
                        const regionData = raw.find(r => r.shop === region);
                        // console.log(regionData, "regionData===");
                        if (regionData && regionData.children) {
                            regionItem.children = regionData.children.map(country => {
                                // const spAuth = shopData.sp[region].IsOuth || 0;
                                // 判断 country.country在不在shopData.sp[region]中的CountryCode

                                let spAuth = 0;
                                if (shopData.sp[region]?.CountryCode?.includes(country.country)) {
                                    spAuth = shopData.sp[region]?.IsOuth || 0;
                                }
                                // console.log(shopData.sp[region]?.ActivateStatus,country, "spAuth===");
                                const spServiceActive = shopData.sp[region]?.ActivateStatus?.find((item: any) => item.CountryCode === country.country);
                                // console.log(spServiceActive, "spServiceActive===");
                                let adAuth = 0;
                                const adRegion = ad_raw.find(item =>
                                    item.children.some(child => child.value === country.country)
                                )?.value;
                                // console.log(adRegion, "adRegion===")

                                if (adRegion && shopData.ad[adRegion]) {
                                    adAuth = shopData.ad[adRegion].IsOuth;
                                }

                                // console.log(shopData.ad[region], "shopData.ad[region]===");

                                // 获取广告授权状态
                                // const adAuth = shopData.ad[region]?.IsOuth || 0;

                                return {
                                    key: `${shopId}-${region}-${country.country}`,
                                    shop: "",
                                    country: country.country,
                                    isSon: true,
                                    advertise: adAuth,
                                    shopauth: spAuth,
                                    parent_shop: shopData.ShopName,
                                    shopauthtime: shopData.sp[region]?.Addtime || '',
                                    adServiceActive: spServiceActive?.AdServiceActive || 0,
                                    adServiceEndDatetime: spServiceActive?.AdServiceEndDatetime || '',
                                    ReportNotEnglish: spServiceActive?.ReportNotEnglish || 0
                                };
                            });
                        }

                        if (regionItem.children.length > 0) {
                            shopItem.children.push(regionItem);
                        }
                    });
                    if (shopItem.children.length === 0) {
                        delete shopItem.children;
                    }
                    shopList.push(shopItem);
                });
                console.log(shopList);
                // shopList 长度 大于0 然后将shopList中跟 当前userInfo.active_shop_id 相同的item 放到第一个 不要重复
                if (shopList.length > 0) {
                    const activeShopIndex = shopList.findIndex(shop => shop.key === userInfo.active_shop_id.toString());
                    if (activeShopIndex > -1) {
                        const [activeShop] = shopList.splice(activeShopIndex, 1);
                        shopList.unshift(activeShop);
                    }
                }
                setRaw(shopList);
            } else {
                setRaw([]);
            }

            setSelectedRowKeys([]);
        } catch (error) {
            console.error('Failed to fetch authorization data:', error);
            setRaw([]);
        }
        setLoading(false);
    };

    const handleShopChange = (values: any) => {
        if (loading) return;
        if (values.type === 'add') {
            handleAddShop(values);
        } else {
            handleEditShop(values);
        }
    }

    // 新增店铺
    const handleAddShop = async (values: any) => {
        const res = await ShopAdd(values);
        console.log(res);
        if (res && res.data) {
            window.$message?.success(t('page.setting.auth.addshopsuccess'));
            setAddShopVisible(false);
            getAuthData();
            toGroupLogin(false, true);
        }
    }

    const handleEditShop = async (values: any) => {
        const res = await ShopChange({
            ShopName: values.ShopName,
            IsDistributor: values.IsDistributor || 0,
            ID: values.key,
        });
        if (res && res.data) {
            window.$message?.success(t('page.setting.auth.modifyshopsuccess'));
            setAddShopVisible(false);
            getAuthData();
            toGroupLogin(false, true);
        }
    };

    // init表单传值
    const initValue = () => {
        setShopModalRecord({
            ShopName: '',
            IsDistributor: 0,
            type: 'add'
        });
    }

    // 删除店铺
    const handleDeleteShop = async (record: any) => {
        if (loading) return;
        setLoading(true);
        console.log(record);
        const res = await ShopDelete({ ID: record.key });
        if (res && res.data) {
            window.$message?.success(t('page.setting.auth.deleteshopsuccess'));
            getAuthData();
            toGroupLogin(false, true);
        } else {
            setLoading(false);
        }
        // console.log(res);
    }

    const rowSelection: TableProps<DataType>['rowSelection'] = {
        selectedRowKeys,
        onChange: (newSelectedRowKeys, selectedRows) => {
            setSelectedRowKeys(newSelectedRowKeys);
            console.log('Selected rows:', selectedRows);
        },
        checkStrictly,
        getCheckboxProps: (record) => {
            if (record.isParent) {
                // console.log(record.children, "record.children===")

                // 如果是undefined，则不可选
                return {
                    disabled: record.children ? false : true,
                    name: record.shop,
                }
            }
            return {
                disabled: false,
                name: record.shop,
            }
        },
    };

    const handleBatchActivate = () => {
        console.log('Batch activate with data:', selectedRowKeys);
        // 创建一个对象来存储每个ID对应的国家列表
        let idCountryMap: Record<string, string[]> = {};
        // const testkey =  ['2-NA-US', '1-NA-CA', '1-NA-MX', '1-NA-BR']
        selectedRowKeys.forEach((key: string) => {
            const parts = key.split('-');
            if (parts.length === 3) {
                const id = parts[0];
                const country = parts[2];

                if (!idCountryMap[id]) {
                    idCountryMap[id] = [];
                }
                idCountryMap[id].push(country);
            }
        });
        // 将对象转换为接口需要的格式
        const formattedData = Object.entries(idCountryMap).map(([id, countries]) => {
            return `"${id}": "${countries.join(',')}"`;
        }).join(', ');

        console.log('Formatted data for API:', formattedData);
        // formattedData= '"1": "US,CA" ,"2:"US,CA"'
        // console.log(formattedData);
        // return
        const url = `/management/buy-auth?data=${encodeURIComponent(formattedData)}`;
        nav(url);

    }

    // 激活店铺
    const handleBatchActivateShop = (record: any) => {
        if (!record.shopauth) {
            // window.$message?.error("请先完成店铺授权");
            window.$notification?.warning({
                message: t('page.setting.auth.authnotcomplete'),
                description: t('page.setting.auth.shopauthnotcomplete'),
                duration: 5,
                placement: 'topRight',
            });
            showModel(record);
            return;
        }
        if (!record.advertise) {
            // 加入小图标
            // window.$message?.error("请先完成广告授权，再进行广告服务激活");
            window.$notification?.warning({
                message: t('page.setting.auth.authnotcomplete'),
                description: t('page.setting.auth.adsauthnotcomplete'),
                duration: 5,
                placement: 'topRight',
            });
            adAuth(record);
            return;
        }

        console.log('Batch activate with data:', record);
        let countries = '';
        let shop = '';
        if (record.shop && record.children?.length > 0) {
            // handleSPAuth(record.shop, "", 'SP', record);
            console.log(record.children);
            // 将children中的country 拼接成字符串
            countries = record.children.map((item: any) => item.country).join(',');
            shop = record.key.split('-')[0];
        } else {
            shop = record.key.split('-')[0];
            countries = record.key.split('-')[2];
        }
        const data = `"${shop}": "${countries}"`;
        // console.log(data);
        // return
        const url = `/management/buy-auth?data=${encodeURIComponent(data)}`;
        nav(url);
    }


    const goToCountry = async (record: any) => {
        Modal.confirm({
            icon: null,
            title: (
                <div className="flex items-center gap-2 mb-2">
                    <div className="w-8 h-8 rounded-full bg-[#E6F4FF] flex items-center justify-center">
                        <InfoCircleFilled className="text-primary text-xl" />
                    </div>
                    <span className="text-[18px] font-medium">{t('page.setting.auth.switchsiteconfirm')}</span>
                </div>
            ),
            width: 480,
            className: 'custom-modal-confirm',
            content: (
                <div className="pt-2 pb-3">
                    {/* 店铺信息卡片 */}
                    <div className="bg-[#FAFAFA] rounded-lg p-4 mb-2">
                        <div className="flex flex-col gap-3">
                            <div className="flex items-center">
                                <span className="text-gray-500 w-20">{t('page.table.columns.shopname')}</span>
                                <span className="font-medium text-[15px]">{record.parent_shop}</span>
                            </div>
                            <div className="flex items-center">
                                <span className="text-gray-500 w-20">{t('page.setting.auth.targetsite')}</span>
                                <div className="flex items-center gap-2">
                                    <Icon
                                        icon={`circle-flags:${record.country.toLowerCase()}`}
                                        width={22}
                                        height={22}
                                        className="rounded-full shadow-sm"
                                    />
                                    <span className="font-medium text-[15px]">{t(`page.setting.country.${record.country}`)}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* 提示信息 */}
                    <div className="bg-[#F0F7FF] border border-[#BAD6FF] rounded-lg p-4">
                        <div className="flex items-start gap-2">
                            <div className="mt-0.5">
                                <InfoCircleOutlined className="text-primary text-base" />
                            </div>
                            <div className="flex-1">
                                <p className="text-primary m-0 text-[14px]">
                                    {t('page.setting.auth.switchsiteconfirmtext')}
                                </p>
                                <p className="text-gray-500 m-0 mt-1 text-[13px]">
                                    {t('page.setting.auth.switchsiteconfirmtext1')}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            ),
            okText: loading ? t('page.login.common.switching') : t('page.login.common.confirm'),
            cancelText: t('page.login.common.back'),
            okButtonProps: {
                loading: loading,
                size: 'large',
                className: 'w-[100px] h-[38px] bg-primary   hover:bg-primary'
            },
            cancelButtonProps: {
                size: 'large',
                className: 'w-[100px] h-[38px]'
            },
            centered: true,
            async onOk() {
                goToCountryApi(record);
            }
        });
    };

    // 跳转指定站点
    const goToCountryApi = async (record: any) => {
        setLoading(true);
        console.log(record, "record===");
        const UID = record.key.split('-')[0]
        const res = await switchUser({ SwitchUserID: Number(UID) })
        if (res && res.data) {
            const res = await toAuthLogin(record.country, UID);
            if (res) {
                window.$message?.success(t('page.setting.auth.switchsuccess'));
                window.location.href = '/ai/listing';
                // nav('/ai/listing');
            }
        } else {
            window.$message?.error(t('page.setting.auth.switchfailed'));
        }
        setTimeout(() => {
            setLoading(false);
        }, 1000);
    }

    useEffect(() => {
        getAuthData();
        if (redirectParam && redirectParam.length > 0) {
            console.log(redirectParam, "redirectParam===");
            // notification?.success({
            //     message: "支付成功",
            //     description: "您已支付成功，站点已激活"
            // });
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }
    }, []);

    useEffect(() => {
        if (userInfo.roles.length > 0) {
            // 角色信息已更新，可以执行相应的逻辑
            console.log('Roles updated:', userInfo.roles);
            if (userInfo.roles.includes("R_ADMIN")) {
                setIsAdmin(true);
            } else {
                setIsAdmin(false);
            }
        }
    }, [userInfo.roles]);

    const operationInstructionsRef = useRef(null);
    return (
        <div className="min-h-500px  max-h-1000px">
            <ALayout >
                {/* 服务说明 */}
                {/* <ServiceDescription /> */}

                <ACard ref={tableWrapperRef} className="flex-col-stretch sm:flex-1-hidden card-wrapper"
                >

                    <Content className="bg-white">
                        <ModelAuthorization defaultRegion={defaultRegion} record={authRecord} visible={visible} onCancel={() => { setVisible(false); getAuthData() }} onOk={handleSPAuth} />
                        <AddShop visible={addShopVisible} onCancel={() => { setAddShopVisible(false); getAuthData() }} onOk={handleShopChange} initialValues={ShopModalRecord} />
                        {/* <ModelAIAIDisease visible={aivisible} regionData={raws} onCancel={() => { setAivisible(false) }} onOk={handleAIDisease} /> */}
                        <div className='flex items-center justify-between mb-4'>
                            <div >
                                {isAdmin && <AButton icon={<PlusOutlined />} loading={loading} type="primary" className="mr-4" onClick={() => { setAddShopVisible(true) }}>{t('page.setting.auth.addshop')}</AButton>}
                            </div>
                            <AButton ref={operationInstructionsRef} type="link" className="pb-4 flex items-center" onClick={() => { window.open('https://deepthought.feishu.cn/docx/LCa4dVdumo6XbVxwYzocponcnvb', '_blank') }}>
                                <Icon icon='tabler:help' className="text-lg mr-[-2px]"></Icon>
                                {t('page.setting.auth.authorizationoperationinstructions')}
                            </AButton>
                        </div>
                        <ATable
                            scroll={{
                                y: 700,
                                x: 702
                            }}
                            key={raws.length}
                            // size="small"
                            className="auth-table"
                            dataSource={raws}
                            // rowSelection={rowSelection}
                            pagination={false}  // 禁用分页，显示所有数据
                            expandable={{
                                expandRowByClick: true,
                                defaultExpandedRowKeys: raws.length > 0 && raws[0].children?.length > 0 ? [raws[0].key, raws[0].children[0].key] : [],
                                // defaultExpandedRowKeys: raws.map(item => item.key.toString()),
                                // 可选：如果需要控制展开/收起操作
                                // onExpand: (expanded, record) => {
                                //     if (expanded) {
                                //         setExpandedKeys(prev => [...prev, record.key]);
                                //     } else {
                                //         setExpandedKeys(prev => prev.filter(key => key !== record.key));
                                //     }
                                // }
                            }}
                            columns={columns}
                        />
                        {/* } */}
                    </Content>

                </ACard>
            </ALayout>
        </div>
    );
}
