import { Button, Dropdown, Tag, Typography, Select } from 'antd';
import type { MenuProps } from 'antd';
import { useSubmit } from 'react-router-dom';
import { useRoute } from '@sa/simple-router';

import { localStg } from '@/utils/storage';
import { selectToken, selectUserInfo } from '@/store/slice/auth';
import { fetchLogout, switchUser } from '@/service/api/auth';
import { getUerName, login, resetStore } from '@/store/slice/auth';
import { store } from '@/store';
import { useLogin } from '@/hooks/common/login';
const UserAvatar = memo(() => {
  const { Text } = Typography;
  const token = useAppSelector(selectToken);
  const { t } = useTranslation();
  const userInfo = useAppSelector(selectUserInfo);
  const submit = useSubmit();
  const route = useRoute();
  const router = useRouterPush();

  const [localUserInfo, setLocalUserInfo] = useState(userInfo);
  const dispatch = useAppDispatch();
  const { toGroupLogin } = useLogin();
  function logout() {
    window?.$modal?.confirm({
      title: t('common.tip'),
      content: t('common.logoutConfirm'),
      okText: t('common.confirm'),
      cancelText: t('common.cancel'),
      onOk: () => {
        let needRedirect = false;
        if (!route.meta?.constant) needRedirect = true;
        // console.log(route.fullPath);
        fetchLogout().then(res => {
          dispatch(resetStore())
          submit({ redirectFullPath: route.fullPath, needRedirect }, { method: 'post', action: '/logout' });

          console.log(res);
        });
        // return;
        // 
      }
    });
  }

  function onClick({ key }: { key: string }) {
    if (key === '2') {
      logout();
    } else {
      router.routerPushByKey('management_user');
    }
  }
  function loginOrRegister() {
    router.routerPushByKey('login');
  }

  const items: MenuProps['items'] = [
    {
      // 展示邮箱信息
      label: (
        <div className="flex flex-col items-center py-2 px-4">
          <div className="w-14 h-14 rounded-full bg-primary flex items-center justify-center text-white font-bold text-xl mb-2">
            {localUserInfo?.nick_name?.charAt(0)?.toUpperCase() || 'D'}
          </div>
          <div className="flex flex-col items-center text-center">
            <span className="font-bold text-base text-black mb-1">
              {localUserInfo?.nick_name}
            </span>
            <span className="text-gray-500 text-sm">
              {localUserInfo?.email}
            </span>
          </div>
        </div>
      ),
      key: 'user-info',
      disabled: true // 禁用点击，仅用于展示
    },
    {
      type: 'divider'
    },
    {
      label: (
        <div className="flex-center gap-8px">
          <SvgIcon
            icon="ph:user-circle"
            className="text-icon"
          />
          {t('common.userCenter')}
        </div>
      ),
      key: '1'
    },
    {
      type: 'divider'
    },
    {
      label: (
        <div className="flex-center gap-8px">
          <SvgIcon
            icon="ph:sign-out"
            className="text-icon"
          />
          {t('common.logout')}
        </div>
      ),
      key: '2'
    }
  ];

  // checkShop
  async function checkShop(sp_shop_id: Number) {
    const res = await switchUser({ sp_shop_id })
    if (res && res.data) {
      // window.$message?.success("切换成功")

      toGroupLogin(false)
    }
  }


  // 转换 group_shop 对象为数组
  const shopOptions = useMemo(() => {
    // console.log(localUserInfo, "localUserInfo===");
    if (!localUserInfo?.all_shop) return [];
    // console.log(localUserInfo.all_shop.show_name, "localUserInfo.all_shop.show_name===");
    return Object.entries(localUserInfo.all_shop.show_name).map(([key, shop]: any) => ({
      label: (
        <div className='flex items-center'>
          <span className="font-500">{key}</span>
          {shop.virtual_shop_vendor ? (
            <span className="ml-1 inline-flex items-center justify-center px-1.5 h-5 text-xs font-medium bg-primary text-white rounded">
              VC
            </span>
          ) : null}
        </div>
      ),
      value: shop.sp_shop_id
    }));
  }, [localUserInfo?.all_shop]);



  // 处理店铺切换
  const handleShopChange = (shopId: number) => {

    checkShop(Number(shopId));
  };






  useEffect(() => {
    // 每次组件渲染时，从本地存储中获取最新的 userInfo
    const storedUserInfo = localStg.get('userInfo');
    // 判断
    if (storedUserInfo && JSON.stringify(storedUserInfo) !== JSON.stringify(localUserInfo)) {
      setLocalUserInfo({
        ...storedUserInfo,
        roles: storedUserInfo.roles || []
      });
      // window.location.reload();
    }
  }, [userInfo, localStg.get('userInfo')]);

  return localUserInfo ? (
    <div className="flex items-center">
      {/* 切换店铺 */}
      {Object.keys(localUserInfo?.all_shop || {}).length > 0 && (
        <Select
          className="mx-2"
          style={{ width: 220 }}
          placeholder={t('page.login.common.switchshop')}
          key={localUserInfo.active_shop_id}
          options={shopOptions}
          onChange={handleShopChange}
          defaultValue={localUserInfo.active_shop_id}
          optionLabelProp="label"
        // label="店铺"
        />
      )}


      {/* 用户信息 */}
      <Dropdown
        placement="bottomRight"
        trigger={['click']}
        menu={{ items, onClick }}
      >
        <div>
          <ButtonIcon className="px-12px">
           
            <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white font-bold text-lg">
            <SvgIcon
              icon="ph:user-circle"
              className="text-icon-large"
            />
          </div>

            <div className="flex flex-col items-baseline">

              <span className="text-16px font-bold">
                {localUserInfo?.nick_name}
                {localUserInfo?.CompanyName && <Tag className="ml-1" style={{
                  color: '#DAA520',
                  borderColor: '#DAA520',
                  background: 'transparent'
                }}>
                  {localUserInfo?.CompanyName}
                </Tag>
                }
              </span>
            </div>
          </ButtonIcon>
        </div>
      </Dropdown>
      {/* 切换店铺 */}
      {/* {localUserInfo?.group_shop?.length > 0 && (
        <Dropdown
          trigger={['click']}
          placement="bottomRight"
          menu={{
            items: localUserInfo?.group_shop?.map((item: any) => ({
              label: (
                <div>
                  <p className="font-bold">{isShowName ? item.UserName : maskSensitiveInfo(item.UserName)}</p>
                  <p>{isShowName ? item.Email : maskSensitiveInfo(item.Email)}</p>
                </div>
              ),
              key: item.ID,
              onClick: () => checkShop(item.ID),
            })) || []
          }}
        >
          <Button type="link" className="ml-2">
            <Text type="success" className="cursor-pointer">切换店铺</Text>
         </Button>
        </Dropdown>
      )} */}
    </div>
  ) : (
    <Button onClick={loginOrRegister}>{t('page.login.common.loginOrRegister')}</Button>
  );
});

export default UserAvatar;
