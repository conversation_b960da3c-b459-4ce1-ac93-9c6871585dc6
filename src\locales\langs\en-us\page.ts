const page: App.I18n.Schema['translation']['page'] = {
  survey: {
    welcome: {
      title: 'Welcome to DeepBI-help us tailor your experience',
      description:
        'Before you start, we have several questions that will help us tailor the DeepBI experience for you.',
      nextButton: 'Next'
    },
    complete: {
      title: 'Thank you for completing the setup!',
      description:
        "We've received your information. Our customer success team will be in touch shortly to help you get started. To begin optimizing your ads, please connect your Amazon store and activate your site.",
      feature1: {
        title: '自动请求评价，以提高你的产品评分',
        subtitle: '使用我们的邮件自动化工具 Follow-Up'
      },
      feature2: {
        title: '监控销售情况，帮助做出更利润的战略决策',
        subtitle: '使用财务分析仪表盘 Profits'
      },
      feature3: {
        title: '实时追踪关键词，了解产品在市场上的表现',
        subtitle: '使用 Keyword Tracker'
      },
      connectButton: 'Connect Amazon store'
    },
    step1: {
      title: 'About You & Your Business',
      firstName: 'First Name',
      firstNamePlaceholder: 'Please enter your first name',
      firstNameRequired: 'Please enter your first name',
      lastName: 'Last Name',
      lastNamePlaceholder: 'Please enter your last name',
      lastNameRequired: 'Please enter your last name',
      companyName: 'Company Name',
      companyNamePlaceholder: 'Please enter your company name',
      companyNameRequired: 'Please enter your company name',
      role: "What's your role in your business?",
      rolePlaceholder: 'Please select your role in business',
      roleRequired: 'Please select your role',
      roleOptions: {
        execute: 'I execute advertising strategies',
        advise: 'I advise on advertising strategies',
        business: 'I set business strategies to grow the business',
        agency: "I'm a third-party agency or service provider",
        other: 'Other'
      },
      otherRolePlaceholder: 'Please specify other role',
      amazonBusiness: 'Where do you mainly operate your Amazon business?',
      amazonBusinessPlaceholder: 'Select your main operating countries/regions',
      amazonBusinessRequired: 'Please select your operating regions',
      hearAbout: 'How did you hear about us?',
      hearAboutPlaceholder: 'Please select how you heard about us',
      hearAboutRequired: 'Please select the source',
      hearAboutOptions: {
        website: 'Official website',
        google: 'Google search',
        social: 'Social media (e.g., Twitter, LinkedIn)',
        amazon: 'Amazon official partner/SPN listing',
        referral: 'Referred by a friend or colleague',
        event: 'Industry event or webinar',
        competitor: 'Switched from another tool',
        other: 'Other (please specify)'
      },
      otherSourcePlaceholder: 'Please specify other source'
    },
    step2: {
      title: 'What matters most to you?',
      description:
        'Help us learn about your current advertising setup and what matters most to you — so we can make sure you get the most value from day one.',
      adExperience: "What's your experience with Amazon Ads?",
      adExperiencePlaceholder: 'Please select your advertising experience',
      adExperienceRequired: 'Please select your advertising experience',
      adExperienceOptions: {
        new: "I'm completely new to Amazon Ads",
        some: 'I have some experience running Amazon Ads',
        experienced: "I'm an experienced Amazon Ads advertiser"
      },
      manageAds: 'How do you currently manage your Amazon Ads?',
      manageAdsPlaceholder: 'Please select how you manage ads',
      manageAdsRequired: 'Please select your ad management method',
      manageAdsOptions: {
        manual: 'I manage ads manually',
        automation: 'I use automation tools',
        both: 'Both manual and automation',
        notManaging: 'Not currently managing ads myself'
      },
      interests: 'What aspects of ad optimization are you most interested in?',
      interestsPlaceholder: 'Please select the aspect you are most interested in',
      interestsRequired: 'Please select your interests',
      interestsOptions: {
        lowerCosts: 'Lower advertising costs (e.g., ACoS)',
        increaseSales: 'Increase total sales',
        saveTime: 'Save time managing ads',
        newProducts: 'Explore ad strategies for new products',
        budgetControl: 'Keep ad spending under control',
        other: 'Other'
      },
      otherInterestsPlaceholder: 'Please specify other interests',
      storeCount: 'How many Amazon stores do you currently manage?',
      storeCountPlaceholder: 'Please select number of stores',
      storeCountRequired: 'Please select store count',
      storeCountOptions: {
        one: 'Just 1',
        twoThree: '2–3',
        fourFive: '4–5',
        moreThanFive: 'More than 5'
      },
      monthlySpend: 'Choose the typical monthly ad spend for one Amazon site.',
      monthlySpendPlaceholder: 'Please select your monthly ad budget',
      monthlySpendRequired: 'Please select monthly budget',
      monthlySpendOptions: {
        lessThan1k: 'Less than $1,000',
        oneToThree: '$1,000 – $3,000',
        threeToTen: '$3,000 – $10,000',
        moreThan10k: 'More than $10,000',
        notSure: 'Not sure yet'
      },
      usedTools: 'Have you used any ad tools before? (Optional)',
      usedToolsPlaceholder: 'Please select tools you have used (optional)',
      usedToolsOptions: {
        helium10: 'Helium 10',
        perpetua: 'Perpetua',
        quartile: 'Quartile',
        pacvue: 'Pacvue',
        other: 'Other (Others)'
      },
      otherToolsPlaceholder: 'Please specify other tools'
    },
    common: {
      previous: 'Previous',
      next: 'Next',
      submit: 'Submit',
      required: 'Required'
    }
  },
  login: {
    common: {
      loginOrRegister: 'Login / Register',
      userNamePlaceholder: 'Please enter user name',
      phonePlaceholder: 'Please enter phone number',
      companyNamePlaceholder: 'Please enter company name',
      codePlaceholder: 'Please enter verification code',
      switching: 'Switching',
      emailPhonePlaceholder: 'Please enter email/phone number',
      captcha: 'Verification Code',
      captchaPlaceholder: 'Please enter Verification Code',
      passwordPlaceholder: 'Please enter password',
      confirmPasswordPlaceholder: 'Please enter password again',
      codeLogin: 'Verification code login',
      confirm: 'Confirm',
      back: 'Back',
      next: 'Next',
      validateSuccess: 'Verification passed',
      loginSuccess: 'Login successfully',
      welcomeBack: 'Welcome back,',
      passwordRule:
        'Please enter a password with at least 8 characters, including uppercase and lowercase letters and numbers',
      agree: 'Please check the agreement',
      agreeText: 'Read and agree to',
      agreeLink: 'ATLAS Software Terms and Conditions',
      switchshop: 'Switch Store'
    },
    description: {
      // Amazon全托管AI广告助手
      title: 'Amazon Full-Service AI Advertising Assistant',
      description: 'Amazon Official Certified SPN Service Provider',
      serviceprice: 'Service Price',
      price: '$299/Country/Month',
      servicepriceunit: '(Up to 50 Listings can be managed per country)',
      applyseller: 'Applicable Seller Types',
      applysellerdesc: 'Premium sellers, Multi-store sellers, High-volume sellers',
      productscope: 'Product Application Range',
      productscope1:
        'Mature Products (with stable order accumulation, competitive conversion rate, significantly outperforming 30% of similar products)',
      productscope1op: 'Noticeable data optimization results can be achieved in about 2 weeks',
      productscope2: 'New Products (newly launched, conversion rate uncertain)',
      productscope2op:
        'After 1-2 weeks of advertising, conversion performance can be diagnosed and determine if further optimization is needed. The system will guide Listing optimization based on AI-discovered competitor information (including images, titles, pricing, detail pages, reviews, etc.)',
      productscope3: 'Semi-mature Products (has advertising history but slow order growth)',
      productscope3op:
        'After 1-2 weeks of advertising, conversion performance can be diagnosed and determine if further optimization is needed. The system will guide Listing optimization based on AI-discovered competitor information (including images, titles, pricing, detail pages, reviews, etc.)',
      systemprinciple: 'AI System Basic Principles',
      systemprinciple1: 'AI Core Objective',
      systemprinciple1op:
        'Automatically discover and explore precise traffic with low cost and high efficiency, quickly finding the most cost-effective traffic sources in the current market.',
      systemprinciple2: 'AI Core Logic',
      systemprinciple2op:
        'Through SP-Asin targeting to multiple relevant competitor positions, the more competitive the conversion rate performance, the larger the competitor traffic scale obtained.',
      systemprinciple3: 'AI Cost Control Strategy',
      systemprinciple3op:
        'During the initial exploration phase, the system precisely controls daily impressions for each Asin and keyword (0-50 impressions per day), quickly identifying effective traffic sources with high click-through and conversion rates.',
      systemprinciple4: 'AI Keyword Selection Strategy',
      systemprinciple4op:
        'Keywords come from actual order vocabulary, precise and efficient. AI does not compete for high-price rankings, automatically adjusts budget and bid configurations based on real-time cost-effectiveness data.',
      supportcountry: 'Supported Countries',
      supportcountry1: 'North America: United States, Canada',
      supportcountry2: 'Europe: United Kingdom, Germany',
      supportcountry3: 'Asia: Japan, India, UAE',
      supportcountry4: 'South America: Brazil',
      supportcountry5: 'Oceania: Australia'
    },
    pwdLogin: {
      title: 'Login',
      rememberMe: 'Remember me',
      forgetPassword: 'Forget password?',
      register: 'Register',
      otherAccountLogin: 'Other Account Login',
      otherLoginMode: 'Other Login Mode',
      superAdmin: 'Super Admin',
      admin: 'Admin',
      user: 'User',
      loginWithGoogle: 'Log in with Google',
      newToDeepBIAtlas: 'New to DeepBI ATLAS?',
      googleLoginFailedToGetUrl: 'Failed to get Google login URL',
      googleLoginPopupBlocked: 'Unable to open login window, please check browser popup settings',
      googleLoginFailed: 'Google login failed, please try again',
      googleLoginVerificationFailed: 'Google login verification failed',
      googleLoginProcessFailed: 'Login processing failed, please try again'
    },
    codeLogin: {
      title: 'Verification Code',
      getCode: 'Send Code',
      reGetCode: '{{time}}s',
      sendCodeSuccess: 'Verification code sent successfully',
      imageCodePlaceholder: 'Please enter image verification code'
    },
    register: {
      title: 'Register',
      agreement: 'I have read and agree to',
      protocol: '《User Agreement》',
      policy: '《Privacy Policy》'
    },
    resetPwd: {
      title: 'Forgot Password',
      forgotPassword: 'Forgot your password?',
      forgotPassworddesc:
        "If you've forgotten your password, don't worry. Simply enter your email address below, and we'll send you an email with a temporary password. Restoring access to your account has never been easier.",
      resetPassword: 'Forgot your Password',
      checkEmail: 'Check the email',
      checkEmaildesc:
        'Password reset instructions have been sent to your email. Check your inbox, including the spam folder if needed. For assistance, contact support.',
      backToLogin: 'Back to login'
    },
    setPassword: {
      title: 'Set Password',
      nokey: 'Missing key',
      nokeydesc: 'The link has expired or the parameters are incorrect, please re-obtain the reset password link',
      success: 'Password set successfully'
    },
    googleCallback: {
      title: 'Google Callback',
      googleAuthorizationFailed: 'Google authorization failed',
      backToLogin: 'Back to login',
      googleAuthorizationCodeNotFound: 'Google authorization code not found',
      googleLoginProcessing: 'Google login processing...',
      googleLoginRedirecting: 'Google login redirecting...'
    },
    bindWeChat: {
      title: 'Bind WeChat'
    }
  },
  signup: {
    title: 'Sign up',
    haveAccount: 'Have an account?',
    login: 'Login',
    email: 'EMAIL',
    emailCode: 'EMAIL CODE',
    emailPlaceholder: 'Email',
    emailRequired: 'This will be your login',
    password: 'PASSWORD',
    passwordPlaceholder: 'Password',
    passwordRequired: 'Choose a strong password',
    passwordTip: 'Password should be 8+ characters: letters, numbers.',
    repeatPassword: 'REPEAT PASSWORD',
    repeatPasswordPlaceholder: 'Repeat Password',
    repeatPasswordRequired: 'Please confirm the password',
    passwordMismatch: 'The two passwords do not match',
    createAccount: 'Create Account',
    signupWithGoogle: 'Sign up with Google'
  },
  about: {
    title: 'About',
    introduction: `DeepBI ATLASis an elegant and powerful admin template, based on the latest front-end technology stack, including React18.3, Vite5, TypeScript, ReactRouter6.4,Redux/toolkitand UnoCSS. It has built-in rich theme configuration and components, strict code specifications, and an automated file routing system. In addition, it also uses the online mock data solution based on ApiFox. DeepBI ATLASprovides you with a one-stop admin solution, no additional configuration, and out of the box. It is also a best practice for learning cutting-edge technologies quickly.`,
    projectInfo: {
      title: 'Project Info',
      version: 'Version',
      latestBuildTime: 'Latest Build Time',
      githubLink: 'Github Link',
      previewLink: 'Preview Link'
    },
    prdDep: 'Production Dependency',
    devDep: 'Development Dependency'
  },
  home: {
    greeting: 'Good morning, {{userName}}, today is another day full of vitality!',
    weatherDesc: 'Today is cloudy to clear, 20℃ - 25℃!',
    projectCount: 'Project Count',
    todo: 'Todo',
    message: 'Message',
    Doc: 'Doc',
    downloadCount: 'Download Count',
    registerCount: 'Register Count',
    schedule: 'Work and rest Schedule',
    study: 'Study',
    work: 'Work',
    rest: 'Rest',
    entertainment: 'Entertainment',
    visitCount: 'Visit Count',
    turnover: 'Turnover',
    dealCount: 'Deal Count',
    creativity: 'Creativity',
    dashboard: {
      description: 'Description',
      selectDateRange: 'Please select date range',
      editCoreMetrics: 'Edit Core Metrics',
      maxSelectMetrics: 'Select up to 6 metrics',
      minSelectMetrics: 'Please select at least one metric',
      previousPeriod: 'Previous period',
      noData: 'No data',
      metrics: {
        ad_spend: {
          title: 'AI Ad Sales',
          description: 'Sales generated by AI-optimized ads.',
          formula: 'AI Ad Sales Ratio = (AI Ad Sales / Ad Sales) × 100%',
          thirdLine: 'AI Ad Sales Ratio:'
        },
        total_sales: {
          title: 'Total Sales',
          description: 'Sum of all sales from both ads and organic traffic.',
          formula: 'Ad Sales + Organic Sales'
        },
        ad_sales_ratio: {
          title: 'AI Ad Cost Ratio',
          description:
            'Percentage of AI ad spending in total ad spending, reflecting the proportion of AI ads in overall ad investment.',
          formula: '(AI Ad Spend / Total Ad Spend) × 100%',
          thirdLine: 'Total Ad Cost:'
        },
        natural_sales: {
          title: 'AI-Driven Organic Sales',
          description: 'Organic sales driven by AI ads, calculated based on AI ad sales ratio.',
          formula: '(AI Sales / SP Total Sales) × Organic Sales',
          thirdLine: 'Percentage of Organic Sales:'
        },
        sales_count: {
          title: 'AI Sales Quantity',
          description: 'Sales quantity from AI ads, calculated based on AI sales ratio.',
          formula: '(AI Order Count / Total Ad Orders) × Total Ad Orders'
        },
        order_count: {
          title: 'AI Order Count',
          description: 'Number of orders from AI ads.',
          formula: 'AI Order Count'
        },
        ad_savings: {
          title: 'AI Ad Cost Savings',
          description:
            'Percentage difference in ACOS between AI and non-AI ads, reflecting cost savings from AI optimization. A positive value indicates more efficient AI ads.',
          formula: [
            'AI Ad Savings = (Non-AI Spend / Non-AI Sales - AI Spend / AI Sales) × 100%',
            'ACOS Reduction = (Previous Period AI Ad Spend - Current Period AI Ad Spend) / Previous Period Ad Sales',
            'AI Contribution Rate = (Previous Period AI Ad Spend - Current Period AI Ad Spend) / (Previous Period Ad Sales - Current Period Ad Sales)'
          ],
          thirdLine1: 'ACOS Red.:',
          thirdLine2: 'AI Contrib.:'
        },
        ai_op_count: {
          title: 'AI Operation Optimization',
          description:
            'Number of operational optimization actions performed by the AI system, including keyword optimization, bid adjustments, etc.',
          formula: 'Operational Workload Saving = AI Operation Optimization Count / 200',
          thirdLine: 'Op Workload Saved:'
        },
        keyword_create: {
          title: 'AI Keyword Optimization',
          description: 'Number of keyword selection and optimization operations performed by the AI system.',
          formula: 'Cumulative count of AI keyword operations'
        },
        targeting_create: {
          title: 'AI Auto ASIN Targeting',
          description: 'Number of ASIN targeting operations automatically performed by the AI system.',
          formula: 'Cumulative count of AI ASIN targeting additions'
        }
      },
      charts: {
        salesTrend: 'Sales Trend',
        aiVsNonAiRatio: 'AI vs Non-AI Ad Sales Ratio',
        aiSalesAndCostRatio: 'AI Sales & Ad Cost Ratio',
        aiDrivenOrganicSalesTrend: 'AI-Driven Organic Sales Trend',
        aiSalesQuantityTrend: 'AI Sales Quantity Trend',
        aiOrderCountTrend: 'AI Order Count Trend',
        aiOptimizationTrend: 'AI Optimization Trend',
        aiKeywordOptimizationTrend: 'AI Keyword Optimization Trend',
        aiAsinAddingTrend: 'AI ASIN Adding Trend',
        adSavingsTrend: 'Ad Cost Savings Trend',
        // 图表数据分类
        sales: 'Sales',
        aiSales: 'AI Sales',
        aiAdCost: 'AI Ad Cost',
        aiDrivenOrganicSales: 'AI-Driven Organic Sales',
        aiAdSales: 'AI Ad Sales',
        nonAiAdSales: 'Non-AI Ad Sales',
        aiSalesQuantity: 'AI Sales Quantity',
        aiOrderCount: 'AI Orders',
        campaignOptimization: 'Campaign Optimization',
        keywordOptimization: 'Keyword Optimization',
        asinOptimization: 'ASIN Optimization',
        aiKeyword: 'AI Keywords',
        asinAdding: 'ASIN Adding',
        adSavings: 'Ad Cost Savings'
      }
    }
  },
  aidrivenads: {
    columns: {
      market: 'Market',
      date: 'Date',
      changeSite: 'Change Site',
      campaignName: 'Campaign Name',
      content: 'Content',
      operate: 'Operate',
      timeRange: 'Time Range'
    },
    title: {
      Impression: 'Impression',
      Clicks: 'Clicks',
      CTR: 'CTR',
      Order: 'Order',
      ConversionRate: 'Conversion Rate',
      ACOS: 'ACOS',
      AdsType: 'Ads type'
    }
  },
  setting: {
    country: {
      CA: 'Canada',
      US: 'United States',
      MX: 'Mexico',
      BR: 'Brazil',
      ES: 'Spain',
      UK: 'United Kingdom',
      GB: 'United Kingdom',
      FR: 'France',
      BE: 'Belgium',
      NL: 'Netherlands',
      DE: 'Germany',
      IT: 'Italy',
      SE: 'Sweden',
      PL: 'Poland',
      EG: 'Egypt',
      TR: 'Turkey',
      SA: 'Saudi Arabia',
      AE: 'United Arab Emirates',
      IN: 'India',
      SG: 'Singapore',
      AU: 'Australia',
      JP: 'Japan',
      NA: 'North America',
      EU: 'Europe',
      IE: 'Ireland'
    },
    authmodel: {
      region: 'Region',
      spauthconfirm: 'Confirm the shop and region you want to authorize',
      authshop: 'Authorize Store',
      startdiagnosis: 'Start Diagnosis',
      title: 'Amazon Authorization',
      regiontitle: 'Please select the region of the account',
      accountname: 'Account Name',
      accountnameplaceholder: 'Used to distinguish different accounts',
      formrule: 'This is a required field',
      auth: 'Auth',
      authsuccess: 'Authorization Success',
      notifyText: 'The account will be automatically authorized for all other sites (if they are open):',
      step: 'Operation Steps',
      step1:
        'Log in to the computer of the Amazon account to be authorized on the account in the login computer, log in to the Amazon account and DeepBI ATLAS account;',
      step3:
        'Select the site to be authorized, then click the authorization button; In the page that is redirected, click authorize to complete the authorization.',
      step2:
        'Small language sites such as Japan and Mexico need to set the report language to English in the Amazon background to obtain normal data.',
      howtysetup: 'How to set?'
    },
    auth: {
      country: 'Country',
      Subscribe: 'Activate AI Ads',
      Unsubscribe: 'Cancel Subscription',
      advertise: 'Ads Authorization',
      report: 'Report Status',
      shop: 'Store',
      shopauth: 'Store Authorization',
      shopauthtime: 'Authorization Time',
      operation: 'Operation',
      cancelauth: 'Cancel Authorization',
      cancelauthdesc: 'This operation will cancel',
      cancelauthdesc1: 'All ASIN authorization, please be careful.',
      updateauth: 'Update Authorization',
      pauseauth: 'Pause Sync',
      advertiseauth: 'Jump to Amazon Advertising Background Authorization?',
      advertiseauthdesc:
        'Click to go to the Amazon Advertising Background to authorize, please ensure to operate in a common environment, avoid associating with other accounts',
      advertiseauthconfirm: 'Confirm the shop and region you want to authorize',
      advertiseauthbtn: 'Go to Authorization',
      serviceexpire: 'Service Expiration Reminder',
      serviceexpire1: 'I know',
      serviceexpire2: 'Go to Renew',
      advertisestatus: 'AI Service Status',
      cancel: 'Canceled',
      expired: 'Expired',
      active: 'Active',
      inactive: 'Inactive',
      remove: 'Days to Remove',
      daystodate: 'Days to Date',
      more: 'More',
      advertisingserviceactivation: 'Ad Service Activation',
      gosite: 'Go to Site',
      extendadvertisingservice: 'Extend Advertising Service',
      addshop: 'Add Store',
      editshop: 'Edit Store',
      addshopsuccess: 'Add Store Success',
      addshopdesc: 'If you are a supplier (VC) account, please contact the sales team to add!',
      isdistributor: 'Is VC Account',
      no: 'No',
      yes: 'Yes',
      advancedsettings: 'Advanced Settings',
      confirmdelete: 'Confirm Delete',
      confirmdelete1: 'You are sure to delete the shop',
      confirmdelete2: 'This operation cannot be reversed',
      authorizationoperationinstructions: 'Authorization Help',
      shopnotauthorized: 'Store Not Authorized',
      shopnotauthorizeddesc: 'Please authorize the shop first',
      modifyshopsuccess: 'Modify Store Success',
      deleteshopsuccess: 'Delete Store Success',
      switchsiteconfirm: 'Switch Site Confirm',
      targetsite: 'Target Site',
      switchsiteconfirmtext: 'Are you sure to switch to this site?',
      switchsiteconfirmtext1: 'After switching, you will be redirected to the AI Listing page',
      authnotcomplete: 'Authorization Not Completed',
      shopauthnotcomplete: 'Please complete the shop authorization first, then activate the ad service',
      adsauthnotcomplete: 'Please complete the ad authorization first, then activate the ad service',
      switchsuccess: 'Switch Success',
      switchfailed: 'Switch Failed',
      reportLanguage: {
        datacollectionexception: 'Collection Exception',
        reportfailed: 'Data Collection Failure Processing Guide',
        reportfaileddesc: 'After system detection, the shop',
        reportfailed1:
          'Due to the default language of the report not being set to English, the data collection failed.',
        reportfailed2: 'Please update the configuration as follows:',
        reportfailed3: 'Amazon Background',
        reportfailed4: 'setting',
        reportfailed5: 'business information',
        reportfailed6: 'Upload Data Processing Report Language',
        reportfailed7: "Set the 'Current Default Language' to English",
        reportfailed8: 'Operation Example:',
        clicktoview: 'Click to View Large Image',
        example1: 'Example 1',
        example2: 'Example 2'
      },
      serviceDescription: {
        title: 'Service Description',
        description1:
          'Ad Service Activation: After activating the ad service, you can manage listings. AI will automatically create ads and optimize ad performance based on settings.',
        description2: 'Cancel Authorization Process:',
        description3:
          'During the ad service period, you can cancel the site authorization. Please first pause or cancel all AI-managed listings in the site. Once all ad placements are fully paused, you can cancel the authorization.',
        description4: 'Note: Canceling authorization does not affect the ad service status.',
        description5:
          'Service Expiration Process: After the ad service expires, all AI-managed listings will automatically pause advertising, and all ads will be removed after 15 days.',
        description6:
          'Normal Running Ads: In the ad service is not expired or activated, you can use the AI ad service normally, the AI will continue to create ads and keep the ad placement.',
        description7:
          'Cancel Authorization Impact: The data and optimization strategies cannot be recovered after canceling the site authorization, please be careful.'
      }
    }
  },
  function: {
    tab: {
      tabOperate: {
        title: 'Tab Operation',
        addTab: 'Add Tab',
        addTabDesc: 'To about page',
        closeTab: 'Close Tab',
        closeCurrentTab: 'Close Current Tab',
        closeAboutTab: 'Close "About" Tab',
        addMultiTab: 'Add Multi Tab',
        addMultiTabDesc1: 'To MultiTab page',
        addMultiTabDesc2: 'To MultiTab page(with query params)'
      },
      tabTitle: {
        title: 'Tab Title',
        changeTitle: 'Change Title',
        change: 'Change',
        resetTitle: 'Reset Title',
        reset: 'Reset'
      }
    },
    multiTab: {
      routeParam: 'Route Param',
      backTab: 'Back function_tab'
    },
    toggleAuth: {
      toggleAccount: 'Toggle Account',
      authHook: 'Auth Hook Function `hasAuth`',
      superAdminVisible: 'Super Admin Visible',
      adminVisible: 'Admin Visible',
      adminOrUserVisible: 'Admin and User Visible'
    },
    request: {
      repeatedErrorOccurOnce: 'Repeated Request Error Occurs Once',
      repeatedError: 'Repeated Request Error',
      repeatedErrorMsg1: 'Custom Request Error 1',
      repeatedErrorMsg2: 'Custom Request Error 2'
    }
  },
  manage: {
    common: {
      status: {
        enable: 'Enable',
        disable: 'Disable'
      }
    },
    subaccounts: {
      title: 'Permission Management',
      description: 'Permission Management Description',
      columns: {
        shopCountry: 'Shop/Country',
        shopName: 'Store Name',
        country: 'Country',
        email: 'Email',
        role: 'Role',
        memberStatus: 'Member Status',
        approvalStatus: 'Approval Status',
        actions: 'Actions'
      },
      roles: {
        none: 'No Permission',
        viewer: 'Viewer - View data only',
        collaborator: 'Collaborator - Can manage and operate',
        activator: 'Activator - Has highest permissions'
      },
      status: {
        noRelation: 'No Relation',
        joined: 'Joined',
        applying: 'Applying',
        removed: 'Removed',
        notApplied: '- Not Applied',
        pending: 'Pending',
        rejected: 'Rejected',
        approved: 'Approved',
        timeout: 'Timeout',
        revoked: 'Revoked'
      },
      actions: {
        transferPermission: 'Transfer',
        removePermission: 'Remove',
        changePermission: 'Change',
        agree: 'Agree',
        reject: 'Reject',
        withdrawApplication: 'Withdraw Application',
        inviteMember: 'Invite Member'
      },
      messages: {
        transferInDevelopment: 'Transfer permission feature in development - ',
        fetchDataFailed: 'Failed to fetch permission data',
        operationSuccess: 'Operation successful',
        operationFailed: 'Operation failed',
        pleaseSelectRole: 'Please select a new role',
        inviteInDevelopment: 'Invite member feature in development'
      },
      confirmMessages: {
        transferPermission: 'Are you sure you want to transfer admin permission to {{email}}?',
        removePermission: 'Are you sure you want to remove permission for {{email}}?',
        changeRole: 'Are you sure you want to change {{email}} permission to {{newRole}}?',
        agreeApplication: 'Are you sure you want to agree to {{email}} permission application?',
        rejectApplication: 'Are you sure you want to reject {{email}} permission application?',
        withdrawApplication: 'Are you sure you want to withdraw {{email}} permission application?'
      },
      confirmTitles: {
        transferPermission: 'Transfer Admin Permission',
        removePermission: 'Remove User Permission',
        changeRole: 'Change User Permission',
        agreeApplication: 'Agree Permission Application',
        rejectApplication: 'Reject Permission Application',
        withdrawApplication: 'Withdraw Permission Application'
      },
      changeRoleModal: {
        title: 'Change Permission',
        description: 'Select a new permission role for user {{email}}:',
        selectRole: 'Please select a new role',
        currentUser: 'Current User',
        currentRole: 'Current Role',
        selectNewRole: 'Select New Role'
      },
      inviteModal: {
        title: 'Invite Member',
        description:
          'Invite new members to join the management team for the current country/region. Invited users will receive email notifications and can apply for corresponding permissions.',
        emailLabel: 'Email Address',
        emailPlaceholder: "Please enter the invitee's email address",
        emailFormatError: 'Please enter a valid email format',
        roleLabel: 'Permission Role',
        rolePlaceholder: 'Please select the permission role to assign',
        roleRequired: 'Please select a permission role',
        sendInvite: 'Send Invite',
        inviteSuccess: 'Invitation sent successfully',
        inviteFailed: 'Failed to send invitation'
      },
      transfer: {
        modalTitle: 'Transfer Administrator Permissions',
        confirmTransfer: 'Confirm Transfer',
        currentAdmin: 'Current Administrator',
        warning: 'Warning',
        warningText:
          'After transfer, you will lose administrator permissions for this site and cannot perform permission management operations!',
        selectUser: 'Select user to transfer to',
        selectUserPlaceholder: 'Please select the user email to transfer permissions to',
        pleaseSelectUser: 'Please select a user to transfer to',
        confirmMessage: 'Confirm transferring administrator permissions from {{fromEmail}} to {{toEmail}}?',
        noUsersTitle: 'No Available Users for Transfer',
        noUsersContent:
          'There are currently no other users in this site to transfer permissions to. Please invite other users to join this site first.'
      },
      roleDescription: 'Role Description',
      statusDescription: 'Status Description',
      statusDescriptionText: {
        memberStatus: 'Member Status: Shows the relationship status between user and shop',
        approvalStatus: 'Approval Status: Shows the approval progress of permission requests'
      }
    },
    role: {
      title: 'Role List',
      roleName: 'Role Name',
      roleCode: 'Role Code',
      roleStatus: 'Role Status',
      roleDesc: 'Role Description',
      menuAuth: 'Menu Auth',
      buttonAuth: 'Button Auth',
      form: {
        roleName: 'Please enter role name',
        roleCode: 'Please enter role code',
        roleStatus: 'Please select role status',
        roleDesc: 'Please enter role description'
      },
      addRole: 'Add Role',
      editRole: 'Edit Role'
    },
    user: {
      title: 'User List',
      userName: 'User Name',
      userGender: 'Gender',
      nickName: 'Nick Name',
      userPhone: 'Phone Number',
      userEmail: 'Email',
      userStatus: 'User Status',
      userRole: 'User Role',
      oldPassword: 'Old Password',
      newPassword: 'New Password',
      confirmPassword: 'Confirm Password',
      form: {
        userName: 'Please enter user name',
        userGender: 'Please select gender',
        nickName: 'Please enter nick name',
        userPhone: 'Please enter phone number',
        userEmail: 'Please enter email',
        userStatus: 'Please select user status',
        userRole: 'Please select user role',
        oldPasswordPlaceholder: 'Please enter old password',
        newPasswordPlaceholder: 'Please enter new password',
        confirmPasswordPlaceholder: 'Please enter confirm password'
      },
      addUser: 'Add User',
      editUser: 'Edit User',
      gender: {
        male: 'Male',
        female: 'Female'
      }
    },
    menu: {
      home: 'Home',
      title: 'Menu List',
      id: 'ID',
      parentId: 'Parent ID',
      menuType: 'Menu Type',
      menuName: 'Menu Name',
      routeName: 'Route Name',
      routePath: 'Route Path',
      pathParam: 'Path Param',
      layout: 'Layout Component',
      page: 'Page Component',
      i18nKey: 'I18n Key',
      icon: 'Icon',
      localIcon: 'Local Icon',
      iconTypeTitle: 'Icon Type',
      order: 'Order',
      constant: 'Constant',
      keepAlive: 'Keep Alive',
      href: 'Href',
      hideInMenu: 'Hide In Menu',
      activeMenu: 'Active Menu',
      multiTab: 'Multi Tab',
      fixedIndexInTab: 'Fixed Index In Tab',
      query: 'Query Params',
      button: 'Button',
      buttonCode: 'Button Code',
      buttonDesc: 'Button Desc',
      menuStatus: 'Menu Status',
      form: {
        home: 'Please select home',
        menuType: 'Please select menu type',
        menuName: 'Please enter menu name',
        routeName: 'Please enter route name',
        routePath: 'Please enter route path',
        pathParam: 'Please enter path param',
        page: 'Please select page component',
        layout: 'Please select layout component',
        i18nKey: 'Please enter i18n key',
        icon: 'Please enter iconify name',
        localIcon: 'Please enter local icon name',
        order: 'Please enter order',
        keepAlive: 'Please select whether to cache route',
        href: 'Please enter href',
        hideInMenu: 'Please select whether to hide menu',
        activeMenu: 'Please select route name of the highlighted menu',
        multiTab: 'Please select whether to support multiple tabs',
        fixedInTab: 'Please select whether to fix in the tab',
        fixedIndexInTab: 'Please enter the index fixed in the tab',
        queryKey: 'Please enter route parameter Key',
        queryValue: 'Please enter route parameter Value',
        button: 'Please select whether it is a button',
        buttonCode: 'Please enter button code',
        buttonDesc: 'Please enter button description',
        menuStatus: 'Please select menu status'
      },
      addMenu: 'Add Menu',
      editMenu: 'Edit Menu',
      addChildMenu: 'Add Child Menu',
      type: {
        directory: 'Directory',
        menu: 'Menu'
      },
      iconType: {
        iconify: 'Iconify Icon',
        local: 'Local Icon'
      }
    },
    userDetail: {
      explain: `This page is solely for demonstrating the powerful capabilities of react-router-dom's loader. The data is random and may not match.`,
      content: `The loader allows network requests and lazy-loaded files to be triggered almost simultaneously, enabling the lazy-loaded files to be parsed while waiting for the network request to complete. Once the network request finishes, the page is displayed all at once. Leveraging React's Fiber architecture, if users find the waiting time too long, they can switch to different pages during the wait. This is an advantage of the React framework and React Router's data loader, as it avoids the conventional sequence of: request lazy-loaded file -> parse -> mount -> send network request -> render page -> display, and eliminates the need for manually adding a loading effect.`
    }
  },
  table: {
    columns: {
      shopname: 'Store Name',
      country: 'Country',
      serviceexpire: 'Service Expiration Time',
      operation: 'Operation',
      day: 'Day',
      hour: 'Hour',
      minute: 'Minute',
      second: 'Second',
      remaining: 'Remaining'
    }
  },
  listingall: {
    column: {
      parentAsin: 'Parent ASIN',
      price: 'Price',
      inventory: 'Inventory',
      totalSales: 'Total Sales',
      adTotalSales: 'Ad Sales',
      acos: 'ACOS',
      naturalSalesRatio: 'Organic Sales%',
      tacos: 'TACOS',
      spAdSalesRatio: 'SP Ad Sales%',
      adTotalCost: 'Ad Total Cost',
      totalOrders: 'Total Orders',
      aiHost: 'AI Management'
    },
    button: {
      batchHost: 'Batch Enable(Parent ASIN)',
      refreshPage: 'Refresh Page'
    },
    tooltip: {
      clickAsinDetails: 'Click to view ASIN details',
      inventoryLessThan: 'Inventory less than {{limit}}, cannot be hosted'
    },
    search: {
      country: 'Country',
      selectCountry: 'Please select country',
      dateRange: 'Date Range',
      near3Days: 'Last 3 Days',
      near7Days: 'Last 7 Days',
      near30Days: 'Last 30 Days',
      dataDetails: 'Data Details',
      parentAsin: 'Parent ASIN',
      inputParentAsin: 'Enter parent ASIN',
      hostingStatus: 'AI Status Managed',
      selectHostingStatus: 'Select AI Status Managed',
      all: 'All',
      hosted: 'AI-Managed',
      unhosted: 'Unmanaged',
      activated: 'Activated'
    },
    message: {
      existingHosted: 'There are already hosted listings',
      hostSuccess: 'AI hosting successful'
    },
    pagination: {
      total: 'Total {{total}} items'
    },
    dashboard: {
      dataOverview: 'Dashboard',
      dataCollecting: 'Initial data sync in progress, Please wait...',
      dataCollectionGuide: 'Data Sync Guide',
      loadingChart: 'Loading chart...',
      reload: 'Reload',
      totalSales: 'Total Sales',
      adTotalSales: 'Ad Total Sales',
      adImpressions: 'Ad Impressions',
      naturalSalesRatio: 'Organic Sales Ratio',
      adSpend: 'Ad Spend',
      totalOrders: 'Total Orders',
      naturalSales: 'Organic Sales',
      clicks: 'Clicks',
      orders: 'Orders',
      ctr: 'CTR',
      conversionRate: 'Conversion Rate'
    },
    notice: {
      adStatsNoSb: 'Statistics do not include Sponsored Brands (SB) ad data.',
      recentDataBias: "Due to delays in Amazon's official data, statistics from the past three days maybe inaccurate.",
      minimumUnit: "Due to delays in Amazon's official data, statistics from the past three days maybe inaccurate."
    },
    modal: {
      title: 'AI Ad Diagnosis',
      currentSite: 'Current diagnose site:',
      remainingCount: 'Remaining diagnosis count this month:',
      info: 'This operation will perform ad diagnosis on all listings of the selected site, which takes 5-10 minutes.\nThe diagnosis process may take some time, please do not operate frequently. After the diagnosis is completed, please go to the site diagnosis record page for details.',
      insufficient: 'Insufficient diagnosis times for current site, please contact sales',
      generating: 'Diagnosis report is being generated, you can view it on the AI diagnosis report page',
      createSuccess: 'Created diagnosis task successfully, please go to AI diagnosis report page to view'
    }
  },
  ailisting: {
    title: 'AI-Managed Listings',
    columns: {
      parentAsin: 'Parent ASIN',
      totalSales: 'Total Sales',
      adSales: 'Ad Sales',
      adSpend: 'Ad Spend',
      organicSales: 'Organic Sales',
      organicSalesRatio: 'Organic Sales Ratio',
      tacos: 'TACOS',
      aiAcosSp: 'AI ACOS (SP)',
      aiAdSpendSp: 'AI Ad Spend (SP)',
      nonAiAcosSp: 'Original Acos(SP)',
      spAdSalesRatio: 'SP Ad Sales Ratio',
      aiAdSales: 'AI Ad Sales',
      aiSalesRatio: 'AI Sales Ratio',
      aiOrderCount: 'AI Order Count',
      keywordSeedCount: 'Keyword Seed Count',
      asinFlowSeedCount: 'ASIN Funnel Seed Count',
      acos: 'ACOS',
      aiDailyBudget: 'AI Daily Budget',
      aiCampaignCount: 'AI Campaign Count',
      targetAcos: 'Target ACOS',
      status: 'Status',
      operations: 'Operations'
    },
    status: {
      preparing: 'Preparing',
      running: 'Running',
      pausing: 'Pausing',
      paused: 'Paused',
      cancelling: 'Terminating',
      recovering: 'Recovering',
      cancelled: 'Cancelled',
      hosted: 'Hosted'
    },
    tooltips: {
      totalSales: 'Ad Sales + Organic Sales',
      adSales: 'AI Ad Sales + Non-AI Ad Sales',
      adSpend: 'AI Ad Spend + Non-AI Ad Spend',
      organicSales: 'Total Sales - Ad Sales',
      organicSalesRatio: 'Organic Sales / Total Sales',
      tacos: '(AI Ad Spend + Non-AI Ad Spend) / (Ad Sales + Organic Sales)',
      aiAcosSp: 'AI SP Ad Spend / AI SP Ad Sales',
      aiSalesRatio: 'AI Ad Sales / (AI Ad Sales + Non-AI Ad Sales)',
      keywordSeedCount: 'Number of keywords in active and paused campaigns',
      asinFlowSeedCount: 'Number of ASINs in active and paused campaigns',
      acos: '(AI Ad Spend + Non-AI Ad Spend) / Ad Sales',
      targetAcos: 'Default is the store-wide ACOS value. Individual ASIN settings can be configured if needed.',
      targetAcosStrategy:
        'Priority is given to the overall expected ACOS, with individual ASIN strategies as supplementary.',
      statusExplanation: 'ASIN ad status',
      errorPrompt: 'Error prompt',
      aiDailyBudget: 'AI campaign daily budget',
      aiAdSales: 'AI SP ad sales',
      nonAiAcosSp: 'Non-AI SP ad spend / Non-AI SP ad sales',
      spAdSalesRatio: 'All SP ad sales / Total ad sales',
      aiOrderCount: 'AI ad campaign order count',
      aiExploreKeywords: 'AI will gradually explore and add more keywords'
    },
    statusDescriptions: {
      preparing: 'AI is preparing ad campaigns and optimization strategy within 24h.',
      running: 'Ads are running. AI is exploring keywords and ASINs.',
      pausing: 'Ads are temporarily paused.',
      paused: 'Ads have been manually stopped. Can be resumed anytime.',
      cancelling: 'Listing is being removed from AI management.',
      recovering: 'Ads are being resumed and optimization is restarting.'
    },
    buttons: {
      timeBudget: 'Time Budget',
      aiDiagnosis: 'AI Listing Analyzer',
      setTargetAcos: 'Set ACOS',
      batchOperations: 'Batch ASIN Management',
      ASINManagement: 'ASIN Management',
      addHostedAsin: 'Add Hosted ASIN',
      hostingGuide: 'AI-Managed Guide',
      refreshPage: 'Refresh Page',
      goToListingPage: 'Batch Enable AI Management',
      adCampaignList: 'Ad Campaign List'
    },
    dropdownMenu: {
      setAcos: 'Set ACOS',
      cancelHosting: 'Remove',
      pauseDelivery: 'Pause',
      resumeDelivery: 'Resume'
    },
    confirmDialogs: {
      cancelHosting: {
        title: 'Confirm cancellation of AI Management?',
        description:
          'After cancelling hosting, AI optimization service will be terminated, ad naming will be changed, and ad delivery will be paused. If you need to continue optimization, you will need to host again.'
      },
      pauseDelivery: {
        title: 'Confirm pause of delivery?',
        description: 'Are you sure you want to pause ad delivery?'
      }
    },
    asinStatus: {
      outOfStock: 'Out of stock',
      highAcos: 'High ACOS',
      lowKeywords: 'Few keywords, high ACOS',
      lowKeywordsGoodAcos: 'Few keywords, good ACOS',
      matureGoodAcos: 'Mature, good ACOS'
    },
    acosStrategy: {
      ultraConservative: '≤ 16%',
      moderatelyConservative: '≤ 20%',
      conservative: '≤ 24%',
      balanced: '≤ 28%',
      aggressive: '≤ 32%',
      ultraAggressive: '≤ 50%',
      custom: 'Custom ≤',
      strategyLabels: {
        ultraConservative: 'Highly Conservative',
        moderatelyConservative: 'Conservative Strategy',
        conservative: 'Moderately Conservative',
        balanced: 'Balanced Strategy',
        aggressive: 'Aggressive Strategy',
        ultraAggressive: 'Highly Aggressive'
      }
    },
    messages: {
      settingSuccess: 'Setting successful',
      settingFailed: 'Setting failed',
      cancelSuccess: 'Cancel successful',
      pauseSuccess: 'Pause successful',
      resumeSuccess: 'Resume successful',
      selectRunningOrPausedOnly: 'Please select only ASINs with Running or Paused status',
      selectRunningOnly: 'Please select only ASINs with Running status',
      selectPausedOnly: 'Please select only ASINs with Paused status',
      getLowBudgetDataFailed: 'Failed to get low budget data',
      cannotSelect24Hours: 'Cannot select 24 hours, please select a shorter time period',
      confirmSubmitDescription:
        'Confirm submission of current time budget strategy? After submission, the system will execute the latest time budget strategy',
      confirmSubmitDescription2:
        'No time budget strategy has been set for the current site, and the normal ad budget will be used after submission.'
    },
    timeBudgetModal: {
      title: 'Time-Based Minimum Budget Setting',
      basicInformation: 'Basic Information',
      timeBudgetSetting: 'Time Budget Setting',
      timeBudgetSettingDescription: 'Please configure the time periods for the minimum daily budget:',
      timeBudgetSettingDescription2: 'Cross-day selection is supported (e.g.23:00-05:00)Times.',
      normalBudget: 'Regular',
      useDescription: 'Use Description',
      useDescription1:
        'If no time budget is configured, or if the current time falls outside of thedefined periods, the regular ad budget will apply by default.',
      useDescription2: 'During defined time periods, the minimum budget will be applied.',
      useDescription3: 'Avoid frequent changes to the time settings, as this may lead tounexpected ad delivery results.'
    },
    SelectAcos: {
      title: 'Overall Expected ACOS',
      acosStrategyConfigurationDescription: 'ACos Target Strategy Guide',
      acosStrategyConfigurationDescription1: 'Set the target Acos value for all AI-managed listings on this site',
      acosStrategyConfigurationDescription2: 'To set custom ACos for individual listings,use batch edit',
      acosStrategyConfigurationDescription3:
        'The overall expected ACOS value of the site will be used as a key optimizationreference. Avoid frequent changes',
      acosStrategyConfigurationDescription4:
        'Ad color classification system, based on the overall expected ACOS value of the site, categorize and visualize listing performance based on ACOS',
      acosStrategyConfigurationDescription5:
        'The custom target ACOS value must be greater than or equal to 16% and less than or equal to 50%',
      acosStrategyConfigurationDescription6:
        'If no Acos target is selected, the system defaults to Acos ≤ 24% for AI-managed listings',
      customAcos: 'Custom ACOS ≤',
      modal: {
        title: 'AI Smart Management - Overall ACOS Target Setting',
        targetAcosValue: 'Target ACOS Value:',
        applyToAll: 'Apply to All AI-managed Listings',
        applyToAllDescription:
          'Enable this option to apply the expected ACOS value to all AI hosted listings, and automatically update the ACOS value for listings that have been set individually',
        applyToAllDescription2:
          'Disable this option to apply the expected ACOS value only to listings that have not been set individually',
        importantTips: 'Important Tips:',
        importantTipsDescription:
          'The Acos target will guide AI optimization for this listing. Avoid frequent changes.',
        customAcosRange: 'Custom ACOS Range: 16% ~ 50%'
      }
    },
    colorExplanation: {
      defaultSorting: '*Sorting defaults to ad delivery color classification',
      tableColumns: {
        colorCategory: 'Color Classification',
        acosCondition: 'ACOS Condition',
        trafficUnitCondition: 'Traffic Unit Condition',
        strategy: 'Strategy'
      },
      items: {
        lowAcosHighTraffic: {
          acos: 'ACOS Value ≤ 24%',
          condition: 'Sufficient Traffic Units',
          strategy: 'Increase traffic units, increase budget'
        },
        lowAcosLowTraffic: {
          acos: 'ACOS Value ≤ 24%',
          condition: 'Few Traffic Units',
          strategy: 'Explore more traffic units, increase traffic units'
        },
        highAcosHighTraffic: {
          acos: 'ACOS Value > 24%',
          condition: 'Sufficient Traffic Units',
          strategy: 'Lower ACOS value, slightly increase traffic units'
        },
        highAcosLowTraffic: {
          acos: 'ACOS Value > 24%',
          condition: 'Few Traffic Units',
          strategy: 'Low budget exploration, control ACOS value'
        },
        lowInventory: {
          acos: 'No specific ACOS condition',
          condition: 'Low inventory or off-season products',
          strategy: 'Extremely low budget, avoid waste'
        },
        dataCollecting: {
          acos: 'No specific ACOS condition',
          condition: 'Data collection in progress',
          strategy: '--'
        }
      },
      trafficUnitDefinition:
        'Traffic Unit Definition: Traffic units are the sum of keywords and ASINs being advertised, one keyword/ASIN has three traffic units.',
      sufficientTrafficDefinition:
        'Sufficient Traffic Definition: More than 150 traffic units is considered sufficient.',
      colorClassificationBasis: 'Color classification is based on AI strategy Acos performance (SP only)'
    },
    acosSettingModal: {
      title: 'AI Smart Management - ACOS Target Setting',
      targetAcosValue: 'Target ACOS Value:',
      customAcos: 'Custom ACOS ≤ {{value}} %',
      optimizationStrategy: 'AI Optimization Strategy:',
      strategyPoints: {
        siteBased:
          "Based on the site's ACOS target, specific listings can be set individually for more precise ad management.",
        referenceIndicator:
          'The ACOS target of the listing will serve as an important reference indicator for AI hosting. Please avoid frequent modifications.'
      },
      customRangeNotice: 'Custom expected ACOS value range: 16% ~ 50%'
    },
    aiDiagnosisModal: {
      title: 'AI Listing Analyzer',
      remainingDiagnosis: 'Remaining diagnosis count this monthCompetitor ASIN',
      yourProductAsin: 'Your Product ASIN',
      enterYourProductAsin: 'Please enter your product ASIN',
      competitorAsin: 'Competitor Comparison ASIN',
      enterCompetitorAsin: 'Please enter competitor comparison ASIN',
      startDiagnosis: 'Start Analyzer',
      betaVersionNote: 'Ony one report is saved at a time in beta,  A new analysis will overwrite the last one.',
      reportDownload: 'Download',
      diagnosisInProgress: '(Report will be ready in about 15 minutes)',
      diagnosisFailed: '(AI diagnosis failed, please diagnose again)',
      clickToDownload: 'Click to download report',
      diagnosisFailedNoDeduction: 'Failed diagnosis does not deduct from diagnosis count, you can try again'
    },
    asinStatusSetter: {
      title: 'Child ASIN Delivery Status',
      childAsinManagement: 'Child ASIN Management',
      inputAsin: 'Enter ASIN',
      adStatus: 'Ad Status',
      statusOptions: {
        all: 'All',
        enabled: 'Enabled',
        disabled: 'Disabled',
        processing: 'Processing'
      },
      columns: {
        asin: 'ASIN',
        adDeliveryStatus: 'Ad Delivery Status',
        operation: 'Operation'
      },
      price: 'Price',
      inventory: 'Inventory',
      processing: 'Processing',
      confirmDelete: 'Confirm delete this ASIN?',
      operationInProgress: '{{asin}} operation is processing, please wait...',
      statusSubmitted: '{{asin}} status has been submitted, please wait for AI processing',
      totalItems: 'Total {{total}} items',
      batchEnabled: 'Selected ASINs have been enabled',
      batchDisabled: 'Selected ASINs have been disabled',
      batchOperationPartialFailed: 'Some operations may have failed, please check the status',
      batchOperationFailed: 'Batch status setting failed',
      selectAsin: 'Please select ASIN first'
    },
    newAsinModal: {
      title: 'Add Hosted ASIN',
      asinOrUrl: 'ASIN or URL',
      asinRequired: 'Please enter ASIN or Amazon product link',
      asinPlaceholder: 'Enter your ASIN or Amazon product link here, one per line, batch adding supported',
      failedAsins: 'Failed ASINs',
      passedAsins: 'Passed ASINs',
      noticeTitle: 'Note:',
      noticeContent1: "The system's authorization minimum unit is parent ASIN, authorized by the entire Listing.",
      noticeContent2:
        'When entering a child ASIN, it will automatically look up its corresponding parent ASIN, and add the entire parent ASIN (and all of its child ASINs) to the authorization list.',
      noticeContent3:
        'If there is no corresponding parent ASIN, the system will treat the child ASIN as a parent ASIN for authorization.',
      noticeContent4:
        'DeepBI system is only effective for parent ASINs with inventory greater than 50, please ensure inventory is greater than 50 before hosting',
      confirmHosting: 'Confirm AI Management',
      expectedAcos: 'Expected ACOS',
      dailyBudget: 'Budget (Daily)',
      back: 'Back',
      confirm: 'Confirm',
      enterAsin: 'Please enter ASIN',
      enterAcos: 'Please enter expected ACOS',
      enterBudget: 'Please enter budget',
      messages: {
        dataCollecting: 'Data is being collected, please try again later',
        checkMessagePage: 'You can visit ',
        messagePage: 'Message Page',
        checkDetails: ' to check details',
        asinAuthorized: 'ASIN already authorized',
        asinNotExist: 'ASIN does not exist',
        insufficientInventory: 'Insufficient inventory',
        asinProcessing: 'ASIN relationship is being retrieved, please wait',
        validationFailed: 'There are ASINs that failed validation, please modify the failed ASINs',
        addSuccess: 'Added successfully',
        addFailed: 'Add failed'
      }
    },
    funnelChart: {
      conversion: 'CVR',
      explanation: 'Explanation:',
      metrics: {
        exposure: 'Impressions',
        clicks: 'Clicks',
        orders: 'Orders',
        exposureDesc: 'Ad impression count',
        clicksDesc: 'Number of times users clicked on ads',
        ordersDesc: 'Number of orders generated through ads',
        percentDesc: 'Percentages represent conversion rates between adjacent layers'
      },
      table: {
        metric: 'Metric',
        metricTooltip: 'Ad Type',
        exposure: 'Impressions',
        exposureTooltip: 'Ad impression count',
        clicks: 'Clicks',
        clicksTooltip: 'Number of times users clicked on ads',
        cpc: 'Avg.CPC',
        cpcTooltip: 'Cost / Clicks',
        amount: 'Ad Sales',
        amountTooltip: 'Total sales generated by ads',
        orders: 'Orders',
        ordersTooltip: 'Number of orders generated through ads'
      }
    },
    competitorStrategy: {
      title: 'Custom Targeting Settings',
      competitorStrategy: 'Custom Targeting',
      categories: {
        asin: 'Competitor ASIN',
        keyword: 'Keywords',
        history: 'Action History'
      },
      subCategories: {
        positive: {
          asin: 'Target Competitor ASINs',
          keyword: 'Target Keywords'
        },
        negative: {
          asin: 'Negative Competitor ASINs',
          keyword: 'Negative Keywords'
        }
      },
      searchPlaceholders: {
        asin: 'Search ASIN',
        keyword: 'Search Keyword',
        history: 'Search ASIN/Keyword'
      },
      addButton: {
        asin: 'Add ASIN',
        keyword: 'Add Keyword'
      },
      emptyText: {
        asin: 'No competitor ASINs',
        negativeAsin: 'No negative competitor ASINs',
        keyword: 'No target keywords',
        negativeKeyword: 'No negative keywords',
        history: 'No history records'
      },
      confirmDelete: {
        asin: 'Confirm delete this ASIN?',
        keyword: 'Confirm delete this keyword?'
      },
      addModal: {
        titlePositiveAsin: 'Add Competitor ASIN',
        titleNegativeAsin: 'Add Negative Competitor ASIN',
        titlePositiveKeyword: 'Add Target Keyword',
        titleNegativeKeyword: 'Add Negative Keyword',
        placeholder: {
          asin: 'Enter one ASIN per line. Youcan add up to 10 ASINs at atime.',
          keyword: 'Enter keywords here, one per line. You can add up to 10 keywords at once and add multiple times.'
        }
      },
      guides: {
        title: 'Operation Guide',
        positiveAsin:
          'AI automatically identifes competitor ASINs for targeting. You can manually add specific ASINs to supplement AI decisions.',
        negativeAsinPoints: [
          'Primarily rely on the AI system for automatic negative competitor ASINs, manual operations are only for supplementation.',
          'Negative competitor ASINs correspond to the "Exact Negative" function in the Amazon Advertising backend.'
        ],
        negativeWarning: {
          title: 'Before adding, make sure the ASIN is not closelyrelated to your product.',
          description: 'Improper exclusions may lead to:',
          consequences: [
            'Missed traffic opportunities',
            'Lower exposure across recommendations',
            'Reduced conversion rate'
          ]
        },
        operationLimit: {
          title: 'Operation Limit:',
          description: 'Avoid excluding more than 10 ASINs per batch to maintain AI optimization performance.'
        },
        positiveKeyword:
          'The AI system has the capability to automatically identify and add keywords. Manual addition of keywords will serve as a supplement to keyword exploration to enhance advertising effectiveness.',
        negativeKeywordPoints:
          'AI automatically idontifies negative ASINs. You may manually exclude ASINs that are irrelevant or perform poor. Negative ASINs function as "Exact Negatives" in Amazon Ads.',
        negativeKeywordWarning: {
          title: 'Before adding, make sure the keyword is not closelyrelated to your product.',
          description:
            'The negated keyword does not have a relationship with your promoted product. If you add related keywords to the negative list, it will lead to:',
          consequences: [
            'The system cannot reach relevant search traffic',
            'Reduced overall conversion rate of the ad group'
          ]
        }
      },
      operationTypes: {
        all: 'All',
        add: 'Add',
        delete: 'Delete'
      },
      processing: 'Processing',
      messages: {
        searchAsinFailed: 'Search ASIN failed',
        searchKeywordFailed: 'Search keyword failed',
        getHistoryFailed: 'Failed to get history records',
        emptyAsin: 'Please enter ASIN',
        emptyKeyword: 'Please enter keyword',
        invalidAsin: 'No valid ASINs',
        invalidKeyword: 'No valid keywords',
        maxLimit: 'Add up to 10 at once',
        addSuccess: 'Added successfully',
        addFailed: 'Add failed',
        deleteSuccess: 'Deleted successfully',
        deleteFailed: 'Delete failed'
      },
      pagination: {
        total: 'Total {{total}} items'
      },
      tableColumns: {
        asin: 'ASIN',
        addTime: 'Add Time',
        addAccount: 'Add Account',
        operation: 'Operation',
        asinKeyword: 'ASIN/Keyword',
        operationType: 'Operation Type',
        operationTime: 'Operation Time',
        keyword: 'Keyword'
      },
      operationTypeLabels: {
        add: 'Add',
        delete: 'Delete'
      },
      confirmMessages: {
        deleteAsin: 'Confirm delete this ASIN?',
        deleteKeyword: 'Confirm delete this keyword?'
      },
      statusLabels: {
        processing: 'Processing'
      }
    }
  },
  mobileDetector: {
    title: 'Reminder',
    description: 'Please use the computer to access this page for the best experience.',
    description1: 'It is recommended to use a display with a resolution of 1920*1080 or higher.',
    copy: 'Copy Link',
    copySuccess: 'Copy Success',
    copyFailed: 'Copy Failed'
  },
  message: {
    title: 'Message Center',
    columns: {
      id: 'ID',
      content: 'Message',
      date: 'Date'
    },
    buttons: {
      markAsRead: 'Mark as Read'
    },
    status: {
      unreadOnly: 'View Unread Only'
    },
    messages: {
      selectMessages: 'Please select messages to mark as read first',
      markSuccess: 'Marked as read successfully'
    }
  },
  camtarListing: {
    title: 'Ad Campaign List',
    columns: {
      campaignName: 'Campaign Name',
      campaignType: 'Type',
      budget: 'Budget',
      status: 'Status',
      orders30Days: 'Orders (30d)',
      spend7Days: 'Spend (7d)',
      clicks7Days: 'Clicks (7d)',
      sales7Days: 'Sales (7d)',
      acos7Days: 'ACOS (7d)',
      spend30Days: 'Spend (30d)',
      clicks30Days: 'Clicks (30d)',
      acos30Days: 'ACOS (30d)',
      sales30Days: 'Sales (30d)',
      spend3Days: 'Spend (3d)',
      clicks3Days: 'Clicks (3d)',
      sales3Days: 'Sales (3d)',
      acos3Days: 'ACOS (3d)',
      operation: 'Operation'
    },
    status: {
      paused: 'Paused',
      enabled: 'Enabled',
      archived: 'Archived'
    },
    search: {
      campaignName: 'Campaign Name',
      campaignNamePlaceholder: 'Please enter campaign name',
      campaignType: 'Campaign Category',
      campaignTypePlaceholder: 'Please select campaign category',
      status: 'Campaign Status',
      statusPlaceholder: 'Please select campaign status',
      date: 'Date',
      all: 'All',
      aiCampaign: 'AI Campaign',
      nonAiCampaign: 'Non-AI Campaign'
    },
    buttons: {
      details: 'Details',
      refresh: 'Refresh Page'
    },
    total: 'Total {{total}} items',
    summary: 'Total'
  },
  targetListing: {
    title: 'Campaign Details',
    columns: {
      targeting: 'Targeting',
      type: 'Type',
      bid: 'Bid',
      status: 'Status',
      orders7Days: 'Orders (7d)',
      spend7Days: 'Spend (7d)',
      sales7Days: 'Sales (7d)',
      impressions7Days: 'Impr. (7d)',
      clicks7Days: 'Clicks (7d)',
      cpc7Days: 'CPC (7d)',
      acos7Days: 'ACOS (7d)',
      spend3Days: 'Spend (3d)',
      sales3Days: 'Sales (3d)',
      impressions3Days: 'Impr. (3d)',
      clicks3Days: 'Clicks (3d)',
      cpc3Days: 'CPC (3d)',
      acos3Days: 'ACOS (3d)',
      spendYesterday: 'Spend (Ytd)',
      salesYesterday: 'Sales (Ytd)',
      impressionsYesterday: 'Impr. (Ytd)',
      clicksYesterday: 'Clicks (Ytd)',
      cpcYesterday: 'CPC (Ytd)',
      acosYesterday: 'ACOS (Ytd)',
      orders30Days: 'Orders (30d)'
    },
    status: {
      paused: 'Paused',
      enabled: 'Enabled',
      archived: 'Archived'
    },
    search: {
      targeting: 'Targeting',
      targetingPlaceholder: 'Please enter targeting',
      status: 'Status',
      statusPlaceholder: 'Please select status',
      date: 'Date'
    },
    buttons: {
      refresh: 'Refresh Page'
    },
    total: 'Total {{total}} items',
    summary: 'Total'
  },
  daily: {
    title: 'Data Overview',
    tabs: {
      current: 'Active Strategy Data',
      canceled: 'Ended Strategy Data'
    },
    buttons: {
      currentAsin: 'Managed ASINs',
      canceledAsin: 'Canceled Managed ASIN Data',
      refresh: 'Refresh Page'
    },
    dateType: {
      day: 'Day',
      week: 'Week'
    },
    columns: {
      date: 'Date',
      adTotalSales: 'Ad Total Sales',
      adTotalSpend: 'Ad Total Spend',
      adTotalAcos: 'Ad Total ACOS',
      aiAdSales: 'AI Ad Sales',
      aiAdSpend: 'AI Ad Spend',
      aiAdTotalAcos: 'AI ACOS',
      originalPlanSales: 'Original Sales',
      originalPlanSpend: 'Original Spend',
      originalPlanAcos: 'Original ACOS',
      totalSales: 'Total Sales',
      naturalSales: 'Natural Sales',
      naturalRatio: 'Natural Ratio',
      tacos: 'TACOS'
    },
    tooltips: {
      adTotalSales: 'All ad sales for managed listings',
      adTotalSpend: 'All ad spend for managed listings',
      adTotalAcos: 'Ad Total Spend (managed) / Ad Total Sales (managed)',
      aiAdSales: 'All AI SP ad sales',
      aiAdSpend: 'All AI SP ad spend',
      aiAdTotalAcos: 'AI Ad Spend / AI Ad Sales',
      originalPlanSales: 'Ad Total Sales (managed) - AI Ad Sales',
      originalPlanSpend: 'Ad Total Spend (managed) - AI Ad Spend',
      originalPlanAcos: 'Original Plan Spend / Original Plan Sales',
      totalSales: 'Order sales for managed listings (ad and non-ad)',
      naturalSales: 'Total Sales (managed) - Ad Total Sales (managed)',
      naturalRatio: 'Natural Sales (managed) / Total Sales (managed)',
      tacos: 'Ad Total Spend (managed) / Total Sales (managed)'
    },
    search: {
      country: 'Country',
      countryPlaceholder: 'Please select country',
      parentAsin: 'Parent ASIN',
      parentAsinPlaceholder: 'Please enter parent ASIN',
      parentAsinTooltip: 'When a single parent ASIN is entered, you can query data for that parent ASIN',
      dateRange: 'Date Range',
      presets: {
        last3Days: 'Last 3 Days',
        last7Days: 'Last 7 Days',
        last30Days: 'Last 30 Days'
      }
    },
    tags: {
      activated: 'Activated'
    },
    summary: 'Total'
  },
  dailyHosted: {
    title: {
      hosted: 'Current Managed ASIN Data',
      nhosted: 'Canceled Managed ASIN Data'
    },
    columns: {
      country: 'Country',
      parentAsin: 'Parent ASIN',
      status: 'Status',
      startDate: 'Start Date',
      operation: 'Operation'
    },
    status: {
      preparing: 'Preparing',
      running: 'Running',
      cancelled: 'Cancelled',
      cancelling: 'Terminating',
      pausing: 'Pausing',
      paused: 'Paused',
      resuming: 'Resuming'
    },
    buttons: {
      viewDetails: 'View Details',
      refresh: 'Refresh Page'
    }
  },
  dailyDetail: {
    title: 'ASIN Data Details',
    buttons: {
      back: 'Back',
      refresh: 'Refresh Page'
    },
    info: {
      asin: 'ASIN',
      country: 'Country',
      type: 'Type'
    },
    types: {
      hosted: 'Currently Managed',
      nhosted: 'Cancelled Management'
    }
  },
  operationRecord: {
    title: 'AI Operation Records',
    subtitle: '(Due to large data volume, only 1 month of data is retained)',
    columns: {
      id: 'ID',
      market: 'Market',
      date: 'Date',
      changeSite: 'Change Site',
      campaignName: 'Campaign Name',
      content: 'Content',
      operate: 'Operation'
    },
    search: {
      country: 'Country',
      date: 'Date'
    }
  },
  listingHistory: {
    title: 'AI Management Records',
    columns: {
      parentAsin: 'Parent ASIN',
      childAsin: 'Child ASIN',
      operationType: 'Operation Type',
      operationAccount: 'Operation Account',
      hostingTime: 'AI Management Time',
      price: 'Price',
      inventory: 'Inventory'
    }
  },
  inventory: {
    title: 'Inventory Management',
    sellableDays: {
      title: 'Sellable Days Color Legend',
      days360: '360 days and above',
      days270: '270 days and above',
      days180: '180 days and above',
      days90: '90 days and above',
      days1: '1 day and above',
      noData: 'No data or 0 days'
    },
    countries: {
      EU: 'European Union',
      UK: 'United Kingdom',
      US: 'United States',
      CA: 'Canada',
      MX: 'Mexico',
      BR: 'Brazil',
      TR: 'Turkey',
      IN: 'India',
      SA: 'Saudi Arabia',
      AE: 'United Arab Emirates',
      JP: 'Japan',
      AU: 'Australia',
      SG: 'Singapore'
    }
  },
  invite: {
    invalidCode: 'Invalid Invitation Code',
    invalidCodeDesc:
      'The invitation code has expired or is invalid. Please contact the inviter to resend the invitation',
    diffUserTitle: 'Account Mismatch',
    diffUserDesc: 'Current logged-in account does not match the invited user email. Please log out and try again',
    logoutAndRetry: 'Logout and Retry',
    googleUserTitle: 'Google User Notice',
    googleUserDesc: 'Google login user, please log in first and then open this link',
    goToLogin: 'Go to Login',
    verifyPasswordTitle: 'Verify Password',
    verifyPasswordDesc: 'Please enter your account password to verify your identity',
    verifyPassword: 'Verify Password',
    passwordVerifyFailed: 'Password verification failed, please check if the password is correct',
    passwordVerifySuccess: 'Password Verified Successfully',
    passwordVerifySuccessDesc: 'Your identity has been verified successfully, you can continue to use the system',
    setPasswordTitle: 'Set Password',
    setPasswordDesc: 'Please set a secure password for your account',
    setPassword: 'Set Password',
    success: 'Operation Successful',
    successDesc: 'Invitation processing completed, you can continue to use the system',
    timeoutTitle: 'Invitation Code Expired',
    timeoutDesc: 'This invitation code has expired, please contact the inviter to resend the invitation',
    havedTitle: 'Invitation Completed',
    havedDesc: 'The user has successfully joined, invitation process is completed',
    completed: 'Processing Completed',
    completedDesc: 'Invitation processing has been completed, welcome to use the system'
  }
};
export default page;
